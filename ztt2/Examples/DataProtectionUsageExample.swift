//
//  DataProtectionUsageExample.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import SwiftUI

/**
 * 数据保护功能使用示例
 * 
 * 展示如何在实际应用中使用数据保护功能
 */
struct DataProtectionUsageExample {
    
    // MARK: - 基本使用示例
    
    /**
     * 应用启动时的数据保护初始化
     */
    static func initializeDataProtection() async {
        print("🚀 初始化数据保护功能...")
        
        let protectionManager = DataProtectionManager.shared
        
        // 执行初始健康检查
        await protectionManager.performHealthCheck()
        
        // 检查是否需要数据修复
        if !protectionManager.isHealthy {
            print("⚠️ 检测到数据问题，开始自动修复...")
            let success = await protectionManager.autoFixDataIssues()
            if success {
                print("✅ 数据修复完成")
            } else {
                print("❌ 数据修复失败，请手动检查")
            }
        }
        
        print("✅ 数据保护功能初始化完成")
    }
    
    /**
     * 定期健康检查（可在后台任务中调用）
     */
    static func performPeriodicHealthCheck() async {
        print("🏥 执行定期健康检查...")
        
        let protectionManager = DataProtectionManager.shared
        await protectionManager.performHealthCheck()
        
        if protectionManager.dataIssuesCount > 0 {
            print("⚠️ 发现 \(protectionManager.dataIssuesCount) 个数据问题")
            
            // 可以选择自动修复或通知用户
            let success = await protectionManager.autoFixDataIssues()
            if success {
                print("✅ 问题已自动修复")
            }
        }
    }
    
    /**
     * 数据同步问题排查
     */
    static func troubleshootSyncIssues() async {
        print("🔍 开始数据同步问题排查...")
        
        let protectionManager = DataProtectionManager.shared
        
        // 1. 执行快速同步测试
        let syncSuccess = await protectionManager.quickSyncTest()
        if !syncSuccess {
            print("❌ 同步测试失败，尝试强制同步...")
            protectionManager.forceCloudKitSync()
        }
        
        // 2. 执行数据一致性检查
        protectionManager.forceDataConsistencyCheck()
        
        // 3. 生成详细报告
        let report = await protectionManager.generateProtectionReport()
        print("📊 详细报告:\n\(report)")
    }
    
    /**
     * 应用重装后的数据恢复
     */
    static func recoverDataAfterReinstall() async {
        print("🔄 开始数据恢复...")
        
        let protectionManager = DataProtectionManager.shared
        
        // 尝试从云端恢复数据
        let success = await protectionManager.restoreData()
        
        if success {
            print("✅ 数据恢复成功")
            
            // 验证数据完整性
            await protectionManager.performHealthCheck()
            
            if protectionManager.isHealthy {
                print("✅ 数据完整性验证通过")
            } else {
                print("⚠️ 数据可能不完整，建议手动检查")
            }
        } else {
            print("❌ 数据恢复失败")
        }
    }
    
    /**
     * 开发调试时的数据诊断
     */
    static func performDevelopmentDiagnostic() {
        print("🧪 执行开发调试诊断...")
        
        let diagnosticTool = DataDiagnosticTool.shared
        
        // 执行完整诊断
        let report = diagnosticTool.performFullDiagnostic()
        
        print("📋 诊断结果:")
        print("• 用户数: \(report.userAnalysis.totalUsers)")
        print("• 成员数: \(report.memberAnalysis.totalMembers)")
        print("• 积分记录数: \(report.pointRecordAnalysis.totalRecords)")
        print("• 发现问题: \(report.consistencyIssues.count) 个")
        
        if !report.consistencyIssues.isEmpty {
            print("⚠️ 问题详情:")
            for issue in report.consistencyIssues {
                print("  - [\(issue.severity.displayText)] \(issue.description)")
            }
        }
        
        // 自动修复问题
        if diagnosticTool.autoFixIssues() {
            print("✅ 问题已自动修复")
        }
    }
    
    /**
     * 多设备同步测试
     */
    static func testMultiDeviceSync() async {
        print("📱 执行多设备同步测试...")
        
        let protectionManager = DataProtectionManager.shared
        let report = await protectionManager.testMultiDeviceSync()
        
        print("📊 测试结果:")
        print("• 总测试数: \(report.totalTests)")
        print("• 通过测试: \(report.passedTests)")
        print("• 成功率: \(String(format: "%.1f", report.successRate * 100))%")
        
        if report.successRate < 0.8 {
            print("⚠️ 同步成功率较低，建议检查网络和iCloud状态")
        }
    }
}

// MARK: - SwiftUI集成示例

/**
 * 数据保护状态视图
 */
struct DataProtectionStatusView: View {
    @StateObject private var protectionManager = DataProtectionManager.shared
    @State private var isLoading = false
    @State private var showingReport = false
    @State private var reportText = ""
    
    var body: some View {
        VStack(spacing: 20) {
            // 状态显示
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: protectionManager.isHealthy ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                        .foregroundColor(protectionManager.isHealthy ? .green : .orange)
                    
                    Text("数据健康状态")
                        .font(.headline)
                    
                    Spacer()
                    
                    Text(protectionManager.isHealthy ? "良好" : "异常")
                        .foregroundColor(protectionManager.isHealthy ? .green : .orange)
                }
                
                if protectionManager.dataIssuesCount > 0 {
                    Text("发现 \(protectionManager.dataIssuesCount) 个数据问题")
                        .foregroundColor(.orange)
                        .font(.caption)
                }
                
                Text("同步状态: \(protectionManager.syncStatus)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if let lastCheck = protectionManager.lastHealthCheck {
                    Text("最后检查: \(DateFormatter.shortDateTime.string(from: lastCheck))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(10)
            
            // 操作按钮
            VStack(spacing: 10) {
                Button("执行健康检查") {
                    Task {
                        isLoading = true
                        await protectionManager.performHealthCheck()
                        isLoading = false
                    }
                }
                .disabled(isLoading)
                
                Button("自动修复问题") {
                    Task {
                        isLoading = true
                        await protectionManager.autoFixDataIssues()
                        isLoading = false
                    }
                }
                .disabled(isLoading || protectionManager.isHealthy)
                
                Button("强制同步") {
                    protectionManager.forceCloudKitSync()
                }
                .disabled(isLoading)
                
                Button("生成详细报告") {
                    Task {
                        isLoading = true
                        reportText = await protectionManager.generateProtectionReport()
                        showingReport = true
                        isLoading = false
                    }
                }
                .disabled(isLoading)
            }
            
            if isLoading {
                ProgressView("处理中...")
                    .padding()
            }
        }
        .padding()
        .sheet(isPresented: $showingReport) {
            NavigationView {
                ScrollView {
                    Text(reportText)
                        .font(.system(.caption, design: .monospaced))
                        .padding()
                }
                .navigationTitle("数据保护报告")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("关闭") {
                            showingReport = false
                        }
                    }
                }
            }
        }
    }
}

// MARK: - 使用说明

/**
 * 在App.swift中的使用示例:
 * 
 * @main
 * struct ztt2App: App {
 *     var body: some Scene {
 *         WindowGroup {
 *             ContentView()
 *                 .task {
 *                     await DataProtectionUsageExample.initializeDataProtection()
 *                 }
 *         }
 *     }
 * }
 * 
 * 在设置页面中添加数据保护状态:
 * 
 * struct SettingsView: View {
 *     var body: some View {
 *         List {
 *             Section("数据保护") {
 *                 DataProtectionStatusView()
 *             }
 *         }
 *     }
 * }
 */
