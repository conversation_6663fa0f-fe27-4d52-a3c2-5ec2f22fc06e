//
//  TrialManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import Combine
import CloudKit

/**
 * 试用管理器
 * 负责管理30天高级会员试用功能
 * 使用NSUbiquitousKeyValueStore进行多设备同步
 * 与CoreData结合存储试用状态和到期时间
 */
@MainActor
class TrialManager: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = TrialManager()
    
    // MARK: - Published Properties
    @Published var hasReceivedTrial: Bool = false
    @Published var isTrialActive: Bool = false
    @Published var trialExpirationDate: Date?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Constants
    private let trialDurationDays = 30
    private let hasReceivedTrialKey = "hasReceivedTrial"
    private let trialExpirationKey = "trialExpirationDate"

    // 本地备份存储键（用于NSUbiquitousKeyValueStore同步延迟的情况）
    private let localHasReceivedTrialKey = "local_hasReceivedTrial"
    private let localTrialExpirationKey = "local_trialExpirationDate"
    
    // MARK: - Private Properties
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    private let dataManager = DataManager.shared
    private let multiLayerStorage = MultiLayerStorageManager.shared
    private var cancellables = Set<AnyCancellable>()
    private var validityCheckTimer: Timer?

    // MARK: - Initialization
    private init() {
        setupObservers()

        // 强制同步NSUbiquitousKeyValueStore
        forceCloudSync()

        // 延迟加载试用状态，确保DataManager已经初始化（等待时间比DataManager的setupCurrentUser更长）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            // 首先尝试数据恢复
            Task {
                await self.performDataRecoveryIfNeeded()
                await MainActor.run {
                    self.loadTrialStatus()
                    // 启动时检查试用有效性
                    self.checkTrialValidity()
                }
            }
        }

        // 设置定期检查定时器（每小时检查一次）
        setupValidityCheckTimer()
    }

    deinit {
        validityCheckTimer?.invalidate()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查用户是否可以领取试用
     */
    func canClaimTrial() -> Bool {
        return !hasReceivedTrial
    }
    
    /**
     * 领取试用
     */
    func claimTrial() async -> Bool {
        guard canClaimTrial() else {
            errorMessage = "trial.error.already_claimed".localized
            return false
        }
        
        isLoading = true
        
        // 计算试用到期时间
        let expirationDate = Calendar.current.date(byAdding: .day, value: trialDurationDays, to: Date())!

        // 更新本地状态
        hasReceivedTrial = true
        isTrialActive = true
        trialExpirationDate = expirationDate

        // 使用多层存储架构保存试用状态
        multiLayerStorage.store(true, forKey: MultiLayerStorageManager.StorageKeys.hasReceivedTrial, syncToCoreData: true)
        multiLayerStorage.store(expirationDate, forKey: MultiLayerStorageManager.StorageKeys.trialExpirationDate, syncToCoreData: true)

        // 兼容性：同时保存到原有存储位置
        ubiquitousStore.set(true, forKey: hasReceivedTrialKey)
        ubiquitousStore.set(expirationDate, forKey: trialExpirationKey)
        ubiquitousStore.synchronize()

        UserDefaults.standard.set(true, forKey: localHasReceivedTrialKey)
        UserDefaults.standard.set(expirationDate, forKey: localTrialExpirationKey)
        UserDefaults.standard.synchronize()

        // 更新CoreData中的订阅信息
        await updateSubscriptionInCoreData(expirationDate: expirationDate)

        isLoading = false

        print("✅ 试用领取成功，到期时间: \(expirationDate)")
        return true
    }
    
    /**
     * 检查试用是否有效
     */
    func checkTrialValidity() {
        guard hasReceivedTrial, let expirationDate = trialExpirationDate else {
            isTrialActive = false
            return
        }
        
        let now = Date()
        let wasActive = isTrialActive
        isTrialActive = now < expirationDate
        
        // 如果试用刚过期，更新CoreData状态
        if wasActive && !isTrialActive {
            Task {
                await handleTrialExpiration()
            }
        }
    }
    
    /**
     * 获取试用剩余天数
     */
    func getRemainingTrialDays() -> Int {
        guard isTrialActive, let expirationDate = trialExpirationDate else {
            return 0
        }
        
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: now, to: expirationDate)
        return max(0, components.day ?? 0)
    }
    
    /**
     * 强制刷新试用状态（用于应用启动时确保状态正确）
     */
    func refreshTrialStatus() {
        print("🔄 强制刷新试用状态...")
        forceCloudSync()

        // 确保DataManager的currentUser已经设置
        dataManager.refreshCurrentUser()

        // 立即重新加载状态
        loadTrialStatus()

        // 再次延迟加载以确保云端同步完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.loadTrialStatus()
        }
    }

    /**
     * 获取试用状态显示信息
     */
    func getTrialDisplayInfo() -> TrialDisplayInfo {
        if !hasReceivedTrial {
            return TrialDisplayInfo(
                status: .available,
                message: "trial.status.available".localized,
                buttonText: "trial.button.claim".localized,
                expirationDate: nil
            )
        } else if isTrialActive {
            let remainingDays = getRemainingTrialDays()
            return TrialDisplayInfo(
                status: .active,
                message: "trial.status.active".localized(with: remainingDays),
                buttonText: "profile.subscription.view_plans_button".localized,
                expirationDate: trialExpirationDate
            )
        } else {
            return TrialDisplayInfo(
                status: .expired,
                message: "trial.status.expired".localized,
                buttonText: "profile.subscription.view_plans_button".localized,
                expirationDate: trialExpirationDate
            )
        }
    }

    // MARK: - Data Recovery Methods

    /**
     * 执行数据恢复（如果需要）
     */
    private func performDataRecoveryIfNeeded() async {
        print("🔍 检查是否需要数据恢复...")

        // 检查当前是否有试用数据
        let hasCurrentData = hasReceivedTrial || trialExpirationDate != nil

        if !hasCurrentData {
            print("🔄 未检测到当前试用数据，尝试从多层存储恢复...")

            // 尝试从多层存储恢复试用状态
            if let recoveredHasReceived: Bool = multiLayerStorage.retrieve(Bool.self, forKey: MultiLayerStorageManager.StorageKeys.hasReceivedTrial) {
                await MainActor.run {
                    self.hasReceivedTrial = recoveredHasReceived
                }
                print("✅ 恢复试用领取状态: \(recoveredHasReceived)")
            }

            if let recoveredExpirationDate: Date = multiLayerStorage.retrieve(Date.self, forKey: MultiLayerStorageManager.StorageKeys.trialExpirationDate) {
                await MainActor.run {
                    self.trialExpirationDate = recoveredExpirationDate
                }
                print("✅ 恢复试用到期时间: \(recoveredExpirationDate)")
            }

            // 触发完整的多层存储恢复
            await multiLayerStorage.performDataRecovery()
        } else {
            print("ℹ️ 检测到现有试用数据，跳过恢复")
        }
    }

    /**
     * 手动触发数据恢复
     */
    func triggerDataRecovery() async {
        print("🔄 手动触发数据恢复...")
        isLoading = true

        await performDataRecoveryIfNeeded()

        // 重新加载试用状态
        await MainActor.run {
            self.loadTrialStatus()
            self.checkTrialValidity()
            self.isLoading = false
        }

        print("✅ 手动数据恢复完成")
    }

    // MARK: - Private Methods

    /**
     * 设置观察者
     */
    private func setupObservers() {
        // 监听NSUbiquitousKeyValueStore变化
        NotificationCenter.default.publisher(for: NSUbiquitousKeyValueStore.didChangeExternallyNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleExternalStoreChange()
                }
            }
            .store(in: &cancellables)
    }

    /**
     * 加载试用状态
     * 使用多层存储架构，优先级：CoreData > NSUbiquitousKeyValueStore > UserDefaults
     */
    private func loadTrialStatus() {
        print("🔍 开始加载试用状态...")

        // 从CoreData加载数据（最高优先级）
        let coreDataHasReceived = dataManager.currentUser?.subscription?.hasReceivedTrial ?? false
        let coreDataExpirationDate = dataManager.currentUser?.subscription?.expirationDate

        // 从NSUbiquitousKeyValueStore加载数据（中等优先级）
        let cloudHasReceived = ubiquitousStore.bool(forKey: hasReceivedTrialKey)
        let cloudExpirationDate = ubiquitousStore.object(forKey: trialExpirationKey) as? Date

        // 从本地UserDefaults加载备份数据（最低优先级）
        let localHasReceived = UserDefaults.standard.bool(forKey: localHasReceivedTrialKey)
        let localExpirationDate = UserDefaults.standard.object(forKey: localTrialExpirationKey) as? Date

        // 使用最可靠的数据源（优先级：CoreData > 云端 > 本地）
        if coreDataHasReceived || coreDataExpirationDate != nil {
            // CoreData有数据，使用CoreData数据（最可靠）
            hasReceivedTrial = coreDataHasReceived
            trialExpirationDate = coreDataExpirationDate

            // 同步用户订阅状态
            if let user = dataManager.currentUser, hasReceivedTrial {
                // 检查试用是否仍然有效
                let isStillValid = coreDataExpirationDate != nil && Date() < coreDataExpirationDate!
                if isStillValid {
                    // 试用仍然有效，设置为高级会员
                    user.subscriptionType = "premium"
                    if let subscription = user.subscription {
                        subscription.subscriptionType = "premium"
                        subscription.level = "premium"
                        subscription.isActive = true
                    }
                } else {
                    // 试用已过期，确保是免费用户
                    user.subscriptionType = "free"
                    if let subscription = user.subscription {
                        subscription.subscriptionType = "free"
                        subscription.level = "free"
                        subscription.isActive = false
                    }
                }
                dataManager.saveContext()
            }

            // 同步到其他存储
            ubiquitousStore.set(coreDataHasReceived, forKey: hasReceivedTrialKey)
            UserDefaults.standard.set(coreDataHasReceived, forKey: localHasReceivedTrialKey)
            if let coreDataDate = coreDataExpirationDate {
                ubiquitousStore.set(coreDataDate, forKey: trialExpirationKey)
                UserDefaults.standard.set(coreDataDate, forKey: localTrialExpirationKey)
            }
            ubiquitousStore.synchronize()
            UserDefaults.standard.synchronize()

            print("📱 从CoreData加载试用状态（最可靠）")
        } else if cloudHasReceived || cloudExpirationDate != nil {
            // 云端有数据，使用云端数据
            hasReceivedTrial = cloudHasReceived
            trialExpirationDate = cloudExpirationDate

            // 同步用户订阅状态
            if let user = dataManager.currentUser, hasReceivedTrial {
                // 检查试用是否仍然有效
                let isStillValid = cloudExpirationDate != nil && Date() < cloudExpirationDate!
                if isStillValid {
                    // 试用仍然有效，设置为高级会员
                    user.subscriptionType = "premium"
                    if let subscription = user.subscription {
                        subscription.subscriptionType = "premium"
                        subscription.level = "premium"
                        subscription.isActive = true
                    }
                } else {
                    // 试用已过期，确保是免费用户
                    user.subscriptionType = "free"
                    if let subscription = user.subscription {
                        subscription.subscriptionType = "free"
                        subscription.level = "free"
                        subscription.isActive = false
                    }
                }
            }

            // 同步到本地备份和CoreData
            UserDefaults.standard.set(cloudHasReceived, forKey: localHasReceivedTrialKey)
            if let cloudDate = cloudExpirationDate {
                UserDefaults.standard.set(cloudDate, forKey: localTrialExpirationKey)
            }
            UserDefaults.standard.synchronize()

            // 同步到CoreData
            if let subscription = dataManager.currentUser?.subscription {
                subscription.hasReceivedTrial = cloudHasReceived
                subscription.expirationDate = cloudExpirationDate
                dataManager.saveContext()
            }

            print("📱 从云端加载试用状态")
        } else if localHasReceived || localExpirationDate != nil {
            // 本地有数据，使用本地备份数据
            hasReceivedTrial = localHasReceived
            trialExpirationDate = localExpirationDate

            // 同步用户订阅状态
            if let user = dataManager.currentUser, hasReceivedTrial {
                // 检查试用是否仍然有效
                let isStillValid = localExpirationDate != nil && Date() < localExpirationDate!
                if isStillValid {
                    // 试用仍然有效，设置为高级会员
                    user.subscriptionType = "premium"
                    if let subscription = user.subscription {
                        subscription.subscriptionType = "premium"
                        subscription.level = "premium"
                        subscription.isActive = true
                    }
                } else {
                    // 试用已过期，确保是免费用户
                    user.subscriptionType = "free"
                    if let subscription = user.subscription {
                        subscription.subscriptionType = "free"
                        subscription.level = "free"
                        subscription.isActive = false
                    }
                }
            }

            // 同步到云端和CoreData
            ubiquitousStore.set(localHasReceived, forKey: hasReceivedTrialKey)
            if let localDate = localExpirationDate {
                ubiquitousStore.set(localDate, forKey: trialExpirationKey)
            }
            ubiquitousStore.synchronize()

            // 同步到CoreData
            if let subscription = dataManager.currentUser?.subscription {
                subscription.hasReceivedTrial = localHasReceived
                subscription.expirationDate = localExpirationDate
                dataManager.saveContext()
            }

            print("📱 从本地备份加载试用状态")
        } else {
            // 都没有数据，初始状态
            hasReceivedTrial = false
            trialExpirationDate = nil
            print("📱 初始化试用状态")
        }

        // 检查试用有效性
        checkTrialValidity()

        // 强制刷新用户状态，确保UI能够立即反映最新的订阅状态
        DispatchQueue.main.async {
            self.dataManager.refreshCurrentUser()
        }

        print("📱 试用状态加载完成 - 已领取: \(hasReceivedTrial), 激活中: \(isTrialActive)")

        // 发送通知，让其他组件知道试用状态已更新
        NotificationCenter.default.post(name: NSNotification.Name("TrialStatusUpdated"), object: nil)
    }

    /**
     * 处理外部存储变化（多设备同步）
     */
    private func handleExternalStoreChange() {
        print("🔄 检测到试用状态外部变化，重新加载...")

        // 获取云端最新数据
        let cloudHasReceived = ubiquitousStore.bool(forKey: hasReceivedTrialKey)
        let cloudExpirationDate = ubiquitousStore.object(forKey: trialExpirationKey) as? Date

        // 更新本地状态
        hasReceivedTrial = cloudHasReceived
        trialExpirationDate = cloudExpirationDate

        // 同步用户订阅状态
        if let user = dataManager.currentUser, hasReceivedTrial {
            // 检查试用是否仍然有效
            let isStillValid = cloudExpirationDate != nil && Date() < cloudExpirationDate!
            if isStillValid {
                // 试用仍然有效，设置为高级会员
                user.subscriptionType = "premium"
                if let subscription = user.subscription {
                    subscription.subscriptionType = "premium"
                    subscription.level = "premium"
                    subscription.isActive = true
                    subscription.hasReceivedTrial = true
                    subscription.expirationDate = cloudExpirationDate
                }
            } else {
                // 试用已过期，确保是免费用户
                user.subscriptionType = "free"
                if let subscription = user.subscription {
                    subscription.subscriptionType = "free"
                    subscription.level = "free"
                    subscription.isActive = false
                    subscription.hasReceivedTrial = true
                    subscription.expirationDate = cloudExpirationDate
                }
            }
            dataManager.saveContext()
        }

        // 同步到本地备份
        UserDefaults.standard.set(cloudHasReceived, forKey: localHasReceivedTrialKey)
        if let cloudDate = cloudExpirationDate {
            UserDefaults.standard.set(cloudDate, forKey: localTrialExpirationKey)
        } else {
            UserDefaults.standard.removeObject(forKey: localTrialExpirationKey)
        }
        UserDefaults.standard.synchronize()

        // 检查试用有效性
        checkTrialValidity()

        print("📱 外部变化处理完成 - 已领取: \(hasReceivedTrial), 激活中: \(isTrialActive)")
    }

    /**
     * 更新CoreData中的订阅信息
     */
    private func updateSubscriptionInCoreData(expirationDate: Date) async {
        guard let user = dataManager.currentUser else {
            print("❌ 无法获取当前用户")
            return
        }

        // 记录旧的订阅级别
        let oldLevel = user.subscription?.subscriptionType ?? "free"

        // 确保用户有订阅记录
        if user.subscription == nil {
            // 创建新的订阅记录
            dataManager.updateSubscription(
                type: "premium",
                isActive: true,
                startDate: Date(),
                endDate: expirationDate,
                productIdentifier: "trial_premium"
            )
            // 设置试用相关字段
            user.subscription?.hasReceivedTrial = true
            user.subscription?.expirationDate = expirationDate
        } else {
            // 更新现有订阅
            user.subscription?.subscriptionType = "premium"
            user.subscription?.level = "premium"
            user.subscription?.isActive = true
            user.subscription?.endDate = expirationDate
            user.subscription?.hasReceivedTrial = true
            user.subscription?.expirationDate = expirationDate
        }

        // 更新用户的订阅类型
        user.subscriptionType = "premium"

        // 保存到CoreData
        dataManager.saveContext()

        print("✅ CoreData订阅信息已更新为试用高级会员")

        // 触发订阅升级通知
        await MainActor.run {
            NotificationCenter.default.post(
                name: NSNotification.Name("SubscriptionUpgraded"),
                object: nil,
                userInfo: [
                    "userId": user.id?.uuidString ?? "",
                    "oldLevel": oldLevel,
                    "newLevel": "premium"
                ]
            )
        }
    }

    /**
     * 处理试用过期
     */
    private func handleTrialExpiration() async {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription else {
            return
        }

        // 记录旧的订阅级别
        let oldLevel = subscription.subscriptionType ?? "premium"

        // 将订阅降级为免费版
        subscription.subscriptionType = "free"
        subscription.level = "free"
        subscription.isActive = false
        subscription.endDate = nil

        // 更新用户的订阅类型
        user.subscriptionType = "free"

        // 保存到CoreData
        dataManager.saveContext()

        print("⏰ 试用已过期，订阅已降级为免费版")

        // 触发订阅降级通知
        await MainActor.run {
            NotificationCenter.default.post(
                name: NSNotification.Name("SubscriptionDowngraded"),
                object: nil,
                userInfo: [
                    "userId": user.id?.uuidString ?? "",
                    "oldLevel": oldLevel,
                    "newLevel": "free"
                ]
            )
        }
    }

    /**
     * 设置试用有效性检查定时器
     */
    private func setupValidityCheckTimer() {
        validityCheckTimer = Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.checkTrialValidity()
            }
        }
    }

    /**
     * 强制同步NSUbiquitousKeyValueStore
     */
    private func forceCloudSync() {
        // 强制同步云端存储
        ubiquitousStore.synchronize()

        // 等待一小段时间让同步完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 再次尝试同步
            self.ubiquitousStore.synchronize()
        }

        print("🔄 强制同步NSUbiquitousKeyValueStore")
    }

    /**
     * 获取当前试用状态的详细信息（用于调试）
     */
    func getTrialStatusDebugInfo() -> String {
        let user = dataManager.currentUser
        let subscription = user?.subscription

        var info = "=== 试用状态调试信息 ===\n"
        info += "TrialManager状态:\n"
        info += "  - hasReceivedTrial: \(hasReceivedTrial)\n"
        info += "  - isTrialActive: \(isTrialActive)\n"
        info += "  - trialExpirationDate: \(trialExpirationDate?.description ?? "nil")\n"

        info += "\n用户订阅状态:\n"
        info += "  - user.subscriptionType: \(user?.subscriptionType ?? "nil")\n"
        info += "  - subscription.subscriptionType: \(subscription?.subscriptionType ?? "nil")\n"
        info += "  - subscription.level: \(subscription?.level ?? "nil")\n"
        info += "  - subscription.isActive: \(subscription?.isActive ?? false)\n"
        info += "  - subscription.hasReceivedTrial: \(subscription?.hasReceivedTrial ?? false)\n"
        info += "  - subscription.expirationDate: \(subscription?.expirationDate?.description ?? "nil")\n"

        info += "\n存储状态:\n"
        info += "  - NSUbiquitousKeyValueStore.hasReceivedTrial: \(ubiquitousStore.bool(forKey: hasReceivedTrialKey))\n"
        info += "  - NSUbiquitousKeyValueStore.trialExpirationDate: \((ubiquitousStore.object(forKey: trialExpirationKey) as? Date)?.description ?? "nil")\n"
        info += "  - UserDefaults.local_hasReceivedTrial: \(UserDefaults.standard.bool(forKey: localHasReceivedTrialKey))\n"
        info += "  - UserDefaults.local_trialExpirationDate: \((UserDefaults.standard.object(forKey: localTrialExpirationKey) as? Date)?.description ?? "nil")\n"

        return info
    }
}

// MARK: - Trial Display Info

struct TrialDisplayInfo {
    let status: TrialStatus
    let message: String
    let buttonText: String
    let expirationDate: Date?
}

enum TrialStatus {
    case available  // 可领取
    case active     // 试用中
    case expired    // 已过期
}
