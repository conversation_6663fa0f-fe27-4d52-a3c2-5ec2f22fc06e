//
//  Persistence.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import CoreData
import CloudKit

/**
 * 持久化控制器
 * 统一使用CloudKit同步，支持所有用户多设备同步
 */
struct PersistenceController {
    static let shared = PersistenceController()

    // CloudKit支持标志 - 现在所有用户都启用CloudKit
    private(set) var isCloudKitEnabled: Bool = true

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext

        // 创建示例用户
        let user = User(context: viewContext)
        user.id = UUID()
        user.nickname = "示例用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()

        // 创建示例订阅
        let subscription = Subscription(context: viewContext)
        subscription.id = UUID()
        subscription.subscriptionType = "free"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user

        // 创建示例家庭成员
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        member.memberNumber = 1
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = user

        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    let container: NSPersistentContainer

    // MARK: - Initialization

    init(inMemory: Bool = false) {
        // 统一使用CloudKit容器，支持所有用户多设备同步
        container = NSPersistentCloudKitContainer(name: "ztt2")

        print("☁️ 配置CloudKit同步容器 - 统一多设备同步模式")

        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        } else {
            Self.configureStoreDescription(container.persistentStoreDescriptions.first!)
        }

        setupContainer()
    }

    // MARK: - Store Configuration

    /**
     * 配置CloudKit存储描述
     */
    private static func configureStoreDescription(_ description: NSPersistentStoreDescription) {
        // 启用CloudKit同步相关配置
        description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

        // CloudKit 容器配置
        description.setOption("iCloud.com.rainkygong.ztt2" as NSString,
                            forKey: "containerIdentifier")

        // 使用默认存储位置
        description.url = defaultStoreURL()

        // 确保自动迁移
        description.setOption(true as NSNumber, forKey: NSMigratePersistentStoresAutomaticallyOption)
        description.setOption(true as NSNumber, forKey: NSInferMappingModelAutomaticallyOption)

        // 确保数据库目录存在
        if let storeURL = description.url {
            let storeDirectory = storeURL.deletingLastPathComponent()
            do {
                try FileManager.default.createDirectory(at: storeDirectory, withIntermediateDirectories: true, attributes: nil)
                print("Core Data store directory created: \(storeDirectory.path)")
            } catch {
                print("Failed to create store directory: \(error)")
            }
        }

        print("Core Data store URL: \(description.url?.path ?? "Unknown")")
    }

    /**
     * 默认存储URL（CloudKit同步）
     */
    private static func defaultStoreURL() -> URL {
        return NSPersistentContainer.defaultDirectoryURL()
            .appendingPathComponent("ztt2.sqlite")
    }

    /**
     * 设置容器
     */
    private func setupContainer() {
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                /*
                 典型的错误原因包括：
                 * 父目录不存在、无法创建或不允许写入
                 * 由于权限或设备锁定时的数据保护，无法访问持久存储
                 * 设备空间不足
                 * 存储无法迁移到当前模型版本
                 * CloudKit 配置问题
                 检查错误消息以确定实际问题
                 */
                print("❌ Core Data error: \(error), \(error.userInfo)")

                // 如果是权限错误，记录错误信息
                if error.code == 513 { // NSCocoaErrorDomain Code=513
                    print("检测到权限错误，可能需要重新安装应用")
                    return
                }

                fatalError("Unresolved error \(error), \(error.userInfo)")
            } else {
                print("✅ Core Data store loaded successfully: \(storeDescription.url?.path ?? "Unknown")")
                print("☁️ CloudKit同步已启用，数据将自动同步到所有设备")
            }
        })

        // 配置视图上下文
        container.viewContext.automaticallyMergesChangesFromParent = true

        // 设置CloudKit远程变更通知
        setupCloudKitNotifications()
    }

    /**
     * 设置CloudKit远程变更通知
     */
    private func setupCloudKitNotifications() {
        // 监听CloudKit远程变更
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { _ in
            print("📥 CloudKit远程变更检测到，刷新本地数据")
            self.container.viewContext.refreshAllObjects()

            // 发送CloudKit同步完成通知，通知UI更新统计数据
            NotificationCenter.default.post(
                name: NSNotification.Name("CloudKitSyncCompleted"),
                object: nil
            )
            print("📊 已发送CloudKit同步完成通知")
        }
    }


}

// MARK: - 数据操作扩展
extension PersistenceController {

    /// 保存上下文（增强版本，包含CloudKit重试机制）
    func save() {
        saveWithRetry(maxRetries: 3, currentAttempt: 1)
    }

    /// 带重试机制的保存方法
    private func saveWithRetry(maxRetries: Int, currentAttempt: Int) {
        let context = container.viewContext

        guard context.hasChanges else {
            print("ℹ️ 没有需要保存的数据变更")
            return
        }

        print("💾 准备保存数据到CloudKit... (尝试 \(currentAttempt)/\(maxRetries))")

        do {
            try context.save()
            print("✅ 数据已保存到本地，CloudKit同步中...")

            // 记录成功保存的时间
            UserDefaults.standard.set(Date(), forKey: "last_successful_save_date")

        } catch {
            print("❌ 数据保存失败: \(error)")

            if let ckError = error as? CKError {
                handleCloudKitError(ckError, maxRetries: maxRetries, currentAttempt: currentAttempt)
            } else {
                handleCoreDataError(error as NSError, maxRetries: maxRetries, currentAttempt: currentAttempt)
            }
        }
    }

    /// 处理CloudKit错误
    private func handleCloudKitError(_ ckError: CKError, maxRetries: Int, currentAttempt: Int) {
        print("CloudKit错误详情: \(ckError.localizedDescription)")
        print("错误代码: \(ckError.code)")

        switch ckError.code {
        case .networkUnavailable, .networkFailure:
            print("🌐 网络不可用，稍后重试...")
            scheduleRetry(maxRetries: maxRetries, currentAttempt: currentAttempt, delay: 5.0)

        case .zoneBusy, .serviceUnavailable:
            print("☁️ CloudKit服务繁忙，稍后重试...")
            scheduleRetry(maxRetries: maxRetries, currentAttempt: currentAttempt, delay: 10.0)

        case .requestRateLimited:
            print("⏱️ 请求频率限制，延长重试间隔...")
            scheduleRetry(maxRetries: maxRetries, currentAttempt: currentAttempt, delay: 30.0)

        case .quotaExceeded:
            print("💾 iCloud存储空间不足，无法重试")

        case .notAuthenticated:
            print("🔐 未登录iCloud账户，无法重试")

        case .permissionFailure:
            print("🚫 权限不足，无法重试")

        default:
            print("❓ 其他CloudKit错误，尝试重试...")
            scheduleRetry(maxRetries: maxRetries, currentAttempt: currentAttempt, delay: 2.0)
        }
    }

    /// 处理CoreData错误
    private func handleCoreDataError(_ nsError: NSError, maxRetries: Int, currentAttempt: Int) {
        print("CoreData错误详情: \(nsError.localizedDescription)")
        print("错误代码: \(nsError.code)")

        // 检查是否是文件不存在的错误，尝试重新初始化
        if nsError.code == 134030 || nsError.code == 4 {
            print("📁 数据库文件问题，尝试重新初始化...")
            container.loadPersistentStores { (storeDescription, error) in
                if error == nil {
                    DispatchQueue.main.async {
                        self.scheduleRetry(maxRetries: maxRetries, currentAttempt: currentAttempt, delay: 1.0)
                    }
                } else {
                    print("❌ 重新初始化失败: \(error?.localizedDescription ?? "未知错误")")
                }
            }
        } else {
            // 其他CoreData错误，尝试重试
            scheduleRetry(maxRetries: maxRetries, currentAttempt: currentAttempt, delay: 2.0)
        }
    }

    /// 安排重试
    private func scheduleRetry(maxRetries: Int, currentAttempt: Int, delay: TimeInterval) {
        guard currentAttempt < maxRetries else {
            print("❌ 已达到最大重试次数，保存失败")
            return
        }

        print("⏰ \(delay) 秒后进行第 \(currentAttempt + 1) 次重试...")

        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            self.saveWithRetry(maxRetries: maxRetries, currentAttempt: currentAttempt + 1)
        }
    }

    /// 触发CloudKit同步（增强版本）
    func triggerCloudKitSync() {
        print("🔄 手动触发CloudKit同步...")

        // 检查上次同步时间，避免频繁同步
        let lastSyncDate = UserDefaults.standard.object(forKey: "last_successful_save_date") as? Date
        let now = Date()

        if let lastSync = lastSyncDate, now.timeIntervalSince(lastSync) < 10 {
            print("⏱️ 距离上次同步时间过短，跳过本次同步")
            return
        }

        // 强制保存当前更改
        save()

        // 刷新所有对象以获取最新的CloudKit数据
        container.viewContext.perform {
            self.container.viewContext.refreshAllObjects()

            DispatchQueue.main.async {
                print("✅ CloudKit同步已触发")

                // 发送同步触发通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("CloudKitSyncTriggered"),
                    object: nil
                )
            }
        }
    }

    /// 强制CloudKit同步（忽略时间限制）
    func forceCloudKitSync() {
        print("🔄 强制触发CloudKit同步...")

        // 强制保存当前更改
        save()

        // 刷新所有对象
        container.viewContext.perform {
            self.container.viewContext.refreshAllObjects()

            DispatchQueue.main.async {
                print("✅ 强制CloudKit同步已触发")

                // 发送强制同步通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("CloudKitForceSyncTriggered"),
                    object: nil
                )
            }
        }
    }

    /// 获取当前用户
    func getCurrentUser() -> User? {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.fetchLimit = 1

        do {
            let users = try container.viewContext.fetch(request)
            return users.first
        } catch {
            print("Failed to fetch user: \(error)")
            return nil
        }
    }

    // 注意：已移除createDefaultUserIfNeeded方法
    // 现在用户必须通过Apple ID登录才能创建账号，确保数据正确关联

    // MARK: - CloudKit Sync Status

    /**
     * 获取CloudKit同步状态
     */
    var cloudKitSyncEnabled: Bool {
        return isCloudKitEnabled
    }


}
