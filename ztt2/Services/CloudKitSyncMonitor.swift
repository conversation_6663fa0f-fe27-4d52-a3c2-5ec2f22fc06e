//
//  CloudKitSyncMonitor.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import CloudKit
import Combine
import UIKit

/**
 * CloudKit同步监控器
 * 监控CloudKit同步状态，管理重试逻辑，提供同步统计信息
 */
@MainActor
class CloudKitSyncMonitor: ObservableObject {
    
    // MARK: - Singleton
    static let shared = CloudKitSyncMonitor()
    
    // MARK: - Published Properties
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var syncSuccessCount: Int = 0
    @Published var syncFailureCount: Int = 0
    @Published var currentRetryAttempt: Int = 0
    @Published var isRetrying: Bool = false
    
    // MARK: - Types
    enum SyncStatus {
        case idle
        case syncing
        case success
        case failed(Error)
        case retrying(attempt: Int, maxAttempts: Int)
        
        var displayText: String {
            switch self {
            case .idle:
                return "空闲"
            case .syncing:
                return "同步中..."
            case .success:
                return "同步成功"
            case .failed(let error):
                return "同步失败: \(error.localizedDescription)"
            case .retrying(let attempt, let maxAttempts):
                return "重试中 (\(attempt)/\(maxAttempts))"
            }
        }
        
        var isActive: Bool {
            switch self {
            case .syncing, .retrying:
                return true
            default:
                return false
            }
        }
    }
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private var cancellables = Set<AnyCancellable>()
    private var retryTimer: Timer?
    
    // MARK: - Configuration
    private let maxRetryAttempts = 5
    private let baseRetryDelay: TimeInterval = 2.0
    private let maxRetryDelay: TimeInterval = 60.0
    
    // MARK: - Initialization
    private init() {
        setupNotificationObservers()
        loadSyncStatistics()
    }
    
    deinit {
        retryTimer?.invalidate()
    }
    
    // MARK: - Public Methods
    
    /**
     * 手动触发同步
     */
    func triggerManualSync() async {
        guard !syncStatus.isActive else {
            print("⚠️ 同步正在进行中，跳过手动触发")
            return
        }
        
        print("🔄 手动触发CloudKit同步...")
        syncStatus = .syncing
        
        await performSync()
    }
    
    /**
     * 重置同步统计
     */
    func resetSyncStatistics() {
        syncSuccessCount = 0
        syncFailureCount = 0
        currentRetryAttempt = 0
        saveSyncStatistics()
        print("🔄 同步统计已重置")
    }
    
    /**
     * 获取同步健康度评分 (0-100)
     */
    func getSyncHealthScore() -> Int {
        let totalAttempts = syncSuccessCount + syncFailureCount
        guard totalAttempts > 0 else { return 100 }
        
        let successRate = Double(syncSuccessCount) / Double(totalAttempts)
        return Int(successRate * 100)
    }
    
    /**
     * 获取同步统计信息
     */
    func getSyncStatistics() -> SyncStatistics {
        return SyncStatistics(
            successCount: syncSuccessCount,
            failureCount: syncFailureCount,
            lastSyncDate: lastSyncDate,
            healthScore: getSyncHealthScore(),
            currentStatus: syncStatus
        )
    }
    
    // MARK: - Private Methods
    
    /**
     * 执行同步
     */
    private func performSync() async {
        do {
            // 触发CoreData保存，这会自动触发CloudKit同步
            coreDataManager.save()
            
            // 等待一段时间让同步完成
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
            
            // 检查同步结果
            if coreDataManager.syncError == nil {
                await handleSyncSuccess()
            } else {
                await handleSyncFailure(coreDataManager.syncError!)
            }
            
        } catch {
            await handleSyncFailure(error)
        }
    }
    
    /**
     * 处理同步成功
     */
    private func handleSyncSuccess() async {
        syncStatus = .success
        lastSyncDate = Date()
        syncSuccessCount += 1
        currentRetryAttempt = 0
        isRetrying = false
        
        retryTimer?.invalidate()
        retryTimer = nil
        
        saveSyncStatistics()
        
        print("✅ CloudKit同步成功")
        
        // 发送同步成功通知
        NotificationCenter.default.post(
            name: NSNotification.Name("CloudKitSyncSuccess"),
            object: nil
        )
    }
    
    /**
     * 处理同步失败
     */
    private func handleSyncFailure(_ error: Error) async {
        syncFailureCount += 1
        
        if let ckError = error as? CKError {
            await handleCloudKitError(ckError)
        } else {
            syncStatus = .failed(error)
            print("❌ 同步失败: \(error.localizedDescription)")
        }
        
        saveSyncStatistics()
    }
    
    /**
     * 处理CloudKit错误
     */
    private func handleCloudKitError(_ error: CKError) async {
        print("🔄 处理CloudKit错误: \(error.localizedDescription)")
        
        // 检查是否应该重试
        if shouldRetry(for: error) && currentRetryAttempt < maxRetryAttempts {
            await scheduleRetry(for: error)
        } else {
            syncStatus = .failed(error)
            currentRetryAttempt = 0
            isRetrying = false
            
            print("❌ CloudKit同步最终失败: \(error.localizedDescription)")
            
            // 发送同步失败通知
            NotificationCenter.default.post(
                name: NSNotification.Name("CloudKitSyncFailed"),
                object: nil,
                userInfo: ["error": error]
            )
        }
    }
    
    /**
     * 判断是否应该重试
     */
    private func shouldRetry(for error: CKError) -> Bool {
        switch error.code {
        case .networkUnavailable, .networkFailure:
            return true
        case .serviceUnavailable, .requestRateLimited:
            return true
        case .zoneBusy:
            return true
        case .quotaExceeded, .notAuthenticated:
            return false // 这些错误不应该重试
        default:
            return true
        }
    }
    
    /**
     * 安排重试
     */
    private func scheduleRetry(for error: CKError) async {
        currentRetryAttempt += 1
        isRetrying = true
        syncStatus = .retrying(attempt: currentRetryAttempt, maxAttempts: maxRetryAttempts)
        
        let retryDelay = calculateRetryDelay(for: error, attempt: currentRetryAttempt)
        
        print("🔄 \(retryDelay)秒后重试CloudKit同步 (第\(currentRetryAttempt)次)")
        
        retryTimer?.invalidate()
        retryTimer = Timer.scheduledTimer(withTimeInterval: retryDelay, repeats: false) { [weak self] _ in
            Task { @MainActor in
                await self?.performSync()
            }
        }
    }
    
    /**
     * 计算重试延迟
     */
    private func calculateRetryDelay(for error: CKError, attempt: Int) -> TimeInterval {
        // 如果CloudKit提供了重试建议时间，使用它
        if let retryAfter = error.retryAfterSeconds {
            return min(retryAfter, maxRetryDelay)
        }
        
        // 根据错误类型和尝试次数计算延迟
        let baseDelay: TimeInterval
        switch error.code {
        case .networkUnavailable, .networkFailure:
            baseDelay = 5.0
        case .serviceUnavailable, .requestRateLimited:
            baseDelay = 10.0
        case .zoneBusy:
            baseDelay = 15.0
        default:
            baseDelay = baseRetryDelay
        }
        
        // 使用指数退避算法
        let exponentialDelay = baseDelay * pow(2.0, Double(attempt - 1))
        return min(exponentialDelay, maxRetryDelay)
    }
    
    /**
     * 设置通知观察者
     */
    private func setupNotificationObservers() {
        // 监听CloudKit同步完成通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CloudKitSyncCompleted"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleSyncSuccess()
            }
        }
        
        // 监听CoreData保存失败通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CoreDataSaveFailed"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let error = notification.userInfo?["error"] as? Error {
                Task { @MainActor in
                    await self?.handleSyncFailure(error)
                }
            }
        }
        
        // 监听应用进入前台
        NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                // 应用进入前台时检查同步状态
                if self?.syncStatus.isActive == false {
                    await self?.triggerManualSync()
                }
            }
        }
    }
    
    /**
     * 加载同步统计
     */
    private func loadSyncStatistics() {
        let userDefaults = UserDefaults.standard
        
        syncSuccessCount = userDefaults.integer(forKey: "cloudkit_sync_success_count")
        syncFailureCount = userDefaults.integer(forKey: "cloudkit_sync_failure_count")
        
        if let lastSync = userDefaults.object(forKey: "cloudkit_last_sync_date") as? Date {
            lastSyncDate = lastSync
        }
    }
    
    /**
     * 保存同步统计
     */
    private func saveSyncStatistics() {
        let userDefaults = UserDefaults.standard
        
        userDefaults.set(syncSuccessCount, forKey: "cloudkit_sync_success_count")
        userDefaults.set(syncFailureCount, forKey: "cloudkit_sync_failure_count")
        
        if let lastSync = lastSyncDate {
            userDefaults.set(lastSync, forKey: "cloudkit_last_sync_date")
        }
        
        userDefaults.synchronize()
    }
}

// MARK: - Supporting Types

struct SyncStatistics {
    let successCount: Int
    let failureCount: Int
    let lastSyncDate: Date?
    let healthScore: Int
    let currentStatus: CloudKitSyncMonitor.SyncStatus
    
    var totalAttempts: Int {
        return successCount + failureCount
    }
    
    var successRate: Double {
        guard totalAttempts > 0 else { return 1.0 }
        return Double(successCount) / Double(totalAttempts)
    }
}
