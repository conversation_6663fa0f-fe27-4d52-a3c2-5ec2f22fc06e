//
//  CoreDataManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import Foundation
import CoreData
import CloudKit
import SwiftUI
import Combine

// MARK: - Data Integrity Types

/**
 * 数据完整性问题类型
 */
enum DataIntegrityIssueType {
    case missingUserID
    case missingUserNickname
    case missingSubscription
    case missingSubscriptionID
    case missingSubscriptionType
    case missingCreatedAt
    case duplicateUsers
    case orphanedMember
    case orphanedPointRecord
    case missingMemberUser
    case missingPointRecordMember
    case invalidRelationship
    case fetchError
}

/**
 * 数据完整性问题
 */
struct DataIntegrityIssue {
    let type: DataIntegrityIssueType
    let entity: NSManagedObject?
    let description: String
    let severity: Severity

    enum Severity {
        case low, medium, high, critical
    }

    init(type: DataIntegrityIssueType, entity: NSManagedObject?, description: String, severity: Severity = .medium) {
        self.type = type
        self.entity = entity
        self.description = description
        self.severity = severity
    }
}

/**
 * Core Data管理器
 * 负责CloudKit同步状态监控和数据一致性管理
 */
@MainActor
class CoreDataManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = CoreDataManager()
    
    // MARK: - Published Properties
    @Published var cloudKitStatus: CloudKitSyncStatus = .notStarted
    @Published var isSyncing: Bool = false
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?
    
    // MARK: - Private Properties
    private let persistenceController = PersistenceController.shared
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Retry Configuration
    private let maxRetryAttempts = 3
    private let baseRetryDelay: TimeInterval = 2.0
    private let maxRetryDelay: TimeInterval = 30.0
    private var currentRetryAttempt = 0
    private var retryTimer: Timer?
    
    // MARK: - Computed Properties
    
    /**
     * 获取视图上下文
     */
    var viewContext: NSManagedObjectContext {
        return persistenceController.container.viewContext
    }
    
    /**
     * 获取CloudKit同步状态
     */
    var cloudKitSyncEnabled: Bool {
        return persistenceController.isCloudKitEnabled
    }
    
    // MARK: - Initialization
    
    private init() {
        setupCloudKitNotifications()
        setupInitialState()
    }
    
    // MARK: - Setup Methods
    
    /**
     * 设置CloudKit通知监听
     */
    private func setupCloudKitNotifications() {
        // 监听CloudKit导入通知
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleCloudKitImport()
            }
        }
        
        // 监听CloudKit导出通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("NSPersistentStoreDidImportUbiquitousContentChanges"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleCloudKitExport()
            }
        }
    }
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        cloudKitStatus = .syncCompleted
        lastSyncDate = UserDefaults.standard.object(forKey: "last_cloudkit_sync_date") as? Date
        
        print("☁️ CoreDataManager初始化完成，CloudKit同步已启用")
    }
    
    // MARK: - CloudKit Sync Handlers
    
    /**
     * 处理CloudKit导入通知
     */
    private func handleCloudKitImport() {
        print("📥 CloudKit数据导入中...")
        
        cloudKitStatus = .syncInProgress
        isSyncing = true
        
        viewContext.perform {
            self.viewContext.refreshAllObjects()
            
            // 执行数据一致性检查和修复
            self.performDataConsistencyCheck()
            
            DispatchQueue.main.async {
                self.cloudKitStatus = .syncCompleted
                self.isSyncing = false
                self.lastSyncDate = Date()
                self.updateLastSyncDate()
                print("✅ CloudKit数据导入完成")
                
                // 发送同步完成通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("CloudKitSyncCompleted"),
                    object: nil
                )
            }
        }
    }
    
    /**
     * 处理CloudKit导出通知
     */
    private func handleCloudKitExport() {
        print("📤 CloudKit数据导出中...")
        
        cloudKitStatus = .syncInProgress
        isSyncing = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.cloudKitStatus = .syncCompleted
            self.isSyncing = false
            self.lastSyncDate = Date()
            self.updateLastSyncDate()
            print("✅ CloudKit数据导出完成")
        }
    }
    
    // MARK: - Data Management
    
    /**
     * 保存上下文（带重试机制）
     */
    func save() {
        saveWithRetry()
    }

    /**
     * 带重试机制的保存方法
     */
    private func saveWithRetry(attempt: Int = 1) {
        do {
            try viewContext.save()

            // 保存成功，重置重试计数
            currentRetryAttempt = 0
            retryTimer?.invalidate()
            retryTimer = nil

            print("✅ 数据保存成功")

        } catch {
            print("❌ 数据保存失败 (尝试 \(attempt)/\(maxRetryAttempts)): \(error)")

            // 如果是CloudKit错误且还有重试机会，则重试
            if let ckError = error as? CKError, attempt < maxRetryAttempts {
                handleCloudKitSaveError(ckError, attempt: attempt)
            } else {
                // 达到最大重试次数或非CloudKit错误
                syncError = error
                cloudKitStatus = .syncFailed(error)
                print("❌ 数据保存最终失败: \(error)")
            }
        }
    }

    /**
     * 处理CloudKit保存错误
     */
    private func handleCloudKitSaveError(_ error: CKError, attempt: Int) {
        print("🔄 处理CloudKit保存错误: \(error.localizedDescription)")

        let retryDelay = calculateRetryDelay(attempt: attempt, error: error)

        switch error.code {
        case .networkUnavailable, .networkFailure:
            print("🌐 网络问题，\(retryDelay)秒后重试...")
            scheduleRetry(after: retryDelay, attempt: attempt)

        case .serviceUnavailable, .requestRateLimited:
            print("⏳ 服务繁忙，\(retryDelay)秒后重试...")
            scheduleRetry(after: retryDelay, attempt: attempt)

        case .zoneBusy:
            print("🏗️ CloudKit区域繁忙，\(retryDelay)秒后重试...")
            scheduleRetry(after: retryDelay, attempt: attempt)

        case .quotaExceeded:
            print("💾 iCloud存储空间不足，停止重试")
            syncError = error
            cloudKitStatus = .syncFailed(error)

        case .notAuthenticated:
            print("🔐 未登录iCloud，停止重试")
            syncError = error
            cloudKitStatus = .syncFailed(error)

        default:
            print("❓ 其他CloudKit错误，\(retryDelay)秒后重试...")
            scheduleRetry(after: retryDelay, attempt: attempt)
        }
    }

    /**
     * 计算重试延迟时间
     */
    private func calculateRetryDelay(attempt: Int, error: CKError) -> TimeInterval {
        // 如果CloudKit提供了重试建议时间，使用它
        if let retryAfter = error.retryAfterSeconds {
            return min(retryAfter, maxRetryDelay)
        }

        // 否则使用指数退避算法
        let exponentialDelay = baseRetryDelay * pow(2.0, Double(attempt - 1))
        return min(exponentialDelay, maxRetryDelay)
    }

    /**
     * 安排重试
     */
    private func scheduleRetry(after delay: TimeInterval, attempt: Int) {
        retryTimer?.invalidate()

        retryTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] _ in
            self?.saveWithRetry(attempt: attempt + 1)
        }

        currentRetryAttempt = attempt
        cloudKitStatus = .syncInProgress
    }
    
    /**
     * 获取当前用户
     */
    func getCurrentUser() -> User? {
        return persistenceController.getCurrentUser()
    }
    
    /**
     * 手动触发CloudKit同步
     */
    func triggerCloudKitSync() {
        guard cloudKitSyncEnabled else {
            print("⚠️ CloudKit同步未启用")
            return
        }
        
        print("🔄 手动触发CloudKit同步...")
        
        cloudKitStatus = .syncInProgress
        isSyncing = true
        
        // 强制保存到CloudKit
        persistenceController.triggerCloudKitSync()
        
        // 刷新所有对象以获取最新的CloudKit数据
        viewContext.refreshAllObjects()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.cloudKitStatus = .syncCompleted
            self.isSyncing = false
            self.lastSyncDate = Date()
            self.updateLastSyncDate()
            print("✅ 手动CloudKit同步完成")
        }
    }
    
    // MARK: - Data Consistency
    
    /**
     * 执行数据一致性检查和修复
     */
    private func performDataConsistencyCheck() {
        print("🔍 开始执行数据一致性检查...")

        var issuesFound = 0
        var issuesFixed = 0

        // 1. 检查用户数据完整性
        let userIssues = checkUserDataIntegrity()
        issuesFound += userIssues.count
        issuesFixed += fixUserDataIssues(userIssues)

        // 2. 检查成员数据完整性
        let memberIssues = checkMemberDataIntegrity()
        issuesFound += memberIssues.count
        issuesFixed += fixMemberDataIssues(memberIssues)

        // 3. 检查积分记录完整性
        let pointRecordIssues = checkPointRecordIntegrity()
        issuesFound += pointRecordIssues.count
        issuesFixed += fixPointRecordIssues(pointRecordIssues)

        // 4. 检查关联关系完整性
        let relationshipIssues = checkRelationshipIntegrity()
        issuesFound += relationshipIssues.count
        issuesFixed += fixRelationshipIssues(relationshipIssues)

        // 5. 检查数据重复问题
        let duplicateIssues = checkForDuplicateData()
        issuesFound += duplicateIssues.count
        issuesFixed += fixDuplicateDataIssues(duplicateIssues)

        // 保存修复结果
        if viewContext.hasChanges {
            do {
                try viewContext.save()
                print("✅ 数据一致性修复已保存")
            } catch {
                print("❌ 保存数据一致性修复失败: \(error)")
            }
        }

        print("✅ 数据一致性检查完成 - 发现问题: \(issuesFound), 修复问题: \(issuesFixed)")
    }
    
    /**
     * 检查用户数据完整性
     */
    private func checkUserDataIntegrity() -> [DataIntegrityIssue] {
        var issues: [DataIntegrityIssue] = []
        let request: NSFetchRequest<User> = User.fetchRequest()

        do {
            let users = try viewContext.fetch(request)

            for user in users {
                // 检查用户基本信息
                if user.id == nil {
                    issues.append(DataIntegrityIssue(type: .missingUserID, entity: user, description: "用户缺少ID", severity: .high))
                }

                if user.nickname?.isEmpty ?? true {
                    issues.append(DataIntegrityIssue(type: .missingUserNickname, entity: user, description: "用户缺少昵称", severity: .medium))
                }

                // 检查订阅信息
                if user.subscription == nil {
                    issues.append(DataIntegrityIssue(type: .missingSubscription, entity: user, description: "用户缺少订阅信息", severity: .high))
                } else if let subscription = user.subscription {
                    // 检查订阅信息的完整性
                    if subscription.id == nil {
                        issues.append(DataIntegrityIssue(type: .missingSubscriptionID, entity: subscription, description: "订阅信息缺少ID", severity: .medium))
                    }

                    if subscription.subscriptionType?.isEmpty ?? true {
                        issues.append(DataIntegrityIssue(type: .missingSubscriptionType, entity: subscription, description: "订阅信息缺少类型", severity: .medium))
                    }
                }

                // 检查创建时间
                if user.createdAt == nil {
                    issues.append(DataIntegrityIssue(type: .missingCreatedAt, entity: user, description: "用户缺少创建时间", severity: .low))
                }
            }

            // 检查重复用户
            let usersByAppleID = Dictionary(grouping: users.compactMap { user in
                user.appleUserID != nil ? (user.appleUserID!, user) : nil
            }, by: { $0.0 })

            for (appleID, duplicateUsers) in usersByAppleID {
                if duplicateUsers.count > 1 {
                    issues.append(DataIntegrityIssue(type: .duplicateUsers, entity: duplicateUsers.first!.1, description: "发现重复的Apple用户ID: \(appleID)", severity: .critical))
                }
            }

        } catch {
            print("❌ 检查用户数据完整性失败: \(error)")
            issues.append(DataIntegrityIssue(type: .fetchError, entity: nil, description: "获取用户数据失败: \(error.localizedDescription)", severity: .critical))
        }

        return issues
    }
    
    /**
     * 检查成员数据完整性
     */
    private func checkMemberDataIntegrity() -> [DataIntegrityIssue] {
        var issues: [DataIntegrityIssue] = []
        let request: NSFetchRequest<Member> = Member.fetchRequest()

        do {
            let members = try viewContext.fetch(request)

            for member in members {
                // 检查成员基本信息
                if member.id == nil {
                    issues.append(DataIntegrityIssue(type: .missingUserID, entity: member, description: "成员缺少ID", severity: .high))
                }

                if member.name?.isEmpty ?? true {
                    issues.append(DataIntegrityIssue(type: .missingUserNickname, entity: member, description: "成员缺少姓名", severity: .medium))
                }

                // 检查成员与用户的关联
                if member.user == nil {
                    issues.append(DataIntegrityIssue(type: .orphanedMember, entity: member, description: "发现孤立的成员（无关联用户）", severity: .critical))
                }

                // 检查创建时间
                if member.createdAt == nil {
                    issues.append(DataIntegrityIssue(type: .missingCreatedAt, entity: member, description: "成员缺少创建时间", severity: .low))
                }

                // 检查积分数据合理性
                if member.currentPoints < 0 {
                    issues.append(DataIntegrityIssue(type: .invalidRelationship, entity: member, description: "成员积分为负数: \(member.currentPoints)", severity: .medium))
                }
            }

        } catch {
            print("❌ 检查成员数据完整性失败: \(error)")
            issues.append(DataIntegrityIssue(type: .fetchError, entity: nil, description: "获取成员数据失败: \(error.localizedDescription)", severity: .critical))
        }

        return issues
    }
    
    /**
     * 检查积分记录完整性
     */
    private func checkPointRecordIntegrity() -> [DataIntegrityIssue] {
        var issues: [DataIntegrityIssue] = []
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()

        do {
            let records = try viewContext.fetch(request)

            for record in records {
                // 检查积分记录基本信息
                if record.id == nil {
                    issues.append(DataIntegrityIssue(type: .missingUserID, entity: record, description: "积分记录缺少ID", severity: .high))
                }

                if record.reason?.isEmpty ?? true {
                    issues.append(DataIntegrityIssue(type: .missingUserNickname, entity: record, description: "积分记录缺少原因", severity: .medium))
                }

                // 检查积分记录与成员的关联
                if record.member == nil {
                    issues.append(DataIntegrityIssue(type: .orphanedPointRecord, entity: record, description: "发现孤立的积分记录（无关联成员）", severity: .critical))
                }

                // 检查时间戳
                if record.timestamp == nil {
                    issues.append(DataIntegrityIssue(type: .missingCreatedAt, entity: record, description: "积分记录缺少时间戳", severity: .medium))
                }

                // 检查记录类型
                if record.recordType?.isEmpty ?? true {
                    issues.append(DataIntegrityIssue(type: .missingSubscriptionType, entity: record, description: "积分记录缺少类型", severity: .low))
                }
            }

        } catch {
            print("❌ 检查积分记录完整性失败: \(error)")
            issues.append(DataIntegrityIssue(type: .fetchError, entity: nil, description: "获取积分记录失败: \(error.localizedDescription)", severity: .critical))
        }

        return issues
    }

    /**
     * 检查关联关系完整性
     */
    private func checkRelationshipIntegrity() -> [DataIntegrityIssue] {
        var issues: [DataIntegrityIssue] = []

        // 检查用户-成员关系
        let userRequest: NSFetchRequest<User> = User.fetchRequest()
        do {
            let users = try viewContext.fetch(userRequest)
            for user in users {
                // 检查用户是否有成员
                if user.members?.count == 0 {
                    issues.append(DataIntegrityIssue(type: .invalidRelationship, entity: user, description: "用户没有任何成员", severity: .medium))
                }
            }
        } catch {
            issues.append(DataIntegrityIssue(type: .fetchError, entity: nil, description: "检查用户-成员关系失败: \(error.localizedDescription)", severity: .critical))
        }

        return issues
    }

    /**
     * 检查重复数据
     */
    private func checkForDuplicateData() -> [DataIntegrityIssue] {
        var issues: [DataIntegrityIssue] = []

        // 检查重复的积分记录
        let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        do {
            let records = try viewContext.fetch(pointRecordRequest)
            let groupedRecords = Dictionary(grouping: records) { record in
                "\(record.member?.id?.uuidString ?? "")-\(record.timestamp?.timeIntervalSince1970 ?? 0)-\(record.value)-\(record.reason ?? "")"
            }

            for (key, duplicateRecords) in groupedRecords {
                if duplicateRecords.count > 1 {
                    issues.append(DataIntegrityIssue(type: .invalidRelationship, entity: duplicateRecords.first, description: "发现重复的积分记录: \(duplicateRecords.count)条", severity: .medium))
                }
            }
        } catch {
            issues.append(DataIntegrityIssue(type: .fetchError, entity: nil, description: "检查重复积分记录失败: \(error.localizedDescription)", severity: .critical))
        }

        return issues
    }

    // MARK: - Data Repair Methods

    /**
     * 修复用户数据问题
     */
    private func fixUserDataIssues(_ issues: [DataIntegrityIssue]) -> Int {
        var fixedCount = 0

        for issue in issues {
            guard let user = issue.entity as? User else { continue }

            switch issue.type {
            case .missingUserID:
                user.id = UUID()
                fixedCount += 1
                print("✅ 修复用户ID缺失")

            case .missingUserNickname:
                user.nickname = "用户\(Int.random(in: 1000...9999))"
                fixedCount += 1
                print("✅ 修复用户昵称缺失")

            case .missingSubscription:
                let subscription = Subscription(context: viewContext)
                subscription.id = UUID()
                subscription.subscriptionType = "free"
                subscription.level = "free"
                subscription.isActive = true
                subscription.createdAt = Date()
                subscription.updatedAt = Date()
                subscription.user = user
                fixedCount += 1
                print("✅ 修复用户订阅信息缺失")

            case .missingCreatedAt:
                user.createdAt = Date()
                fixedCount += 1
                print("✅ 修复用户创建时间缺失")

            case .duplicateUsers:
                // 对于重复用户，保留最新的，删除其他的
                // 这里需要更复杂的逻辑来处理数据合并
                print("⚠️ 发现重复用户，需要手动处理")

            default:
                break
            }
        }

        return fixedCount
    }

    /**
     * 修复成员数据问题
     */
    private func fixMemberDataIssues(_ issues: [DataIntegrityIssue]) -> Int {
        var fixedCount = 0

        for issue in issues {
            guard let member = issue.entity as? Member else { continue }

            switch issue.type {
            case .missingUserID:
                member.id = UUID()
                fixedCount += 1
                print("✅ 修复成员ID缺失")

            case .missingUserNickname:
                member.name = "成员\(Int.random(in: 1000...9999))"
                fixedCount += 1
                print("✅ 修复成员姓名缺失")

            case .orphanedMember:
                // 删除孤立的成员
                viewContext.delete(member)
                fixedCount += 1
                print("✅ 删除孤立的成员")

            case .missingCreatedAt:
                member.createdAt = Date()
                member.updatedAt = Date()
                fixedCount += 1
                print("✅ 修复成员创建时间缺失")

            case .invalidRelationship:
                if member.currentPoints < 0 {
                    member.currentPoints = 0
                    fixedCount += 1
                    print("✅ 修复成员负积分")
                }

            default:
                break
            }
        }

        return fixedCount
    }

    /**
     * 修复积分记录问题
     */
    private func fixPointRecordIssues(_ issues: [DataIntegrityIssue]) -> Int {
        var fixedCount = 0

        for issue in issues {
            guard let record = issue.entity as? PointRecord else { continue }

            switch issue.type {
            case .missingUserID:
                record.id = UUID()
                fixedCount += 1
                print("✅ 修复积分记录ID缺失")

            case .missingUserNickname:
                record.reason = "系统修复记录"
                fixedCount += 1
                print("✅ 修复积分记录原因缺失")

            case .orphanedPointRecord:
                // 删除孤立的积分记录
                viewContext.delete(record)
                fixedCount += 1
                print("✅ 删除孤立的积分记录")

            case .missingCreatedAt:
                record.timestamp = Date()
                fixedCount += 1
                print("✅ 修复积分记录时间戳缺失")

            case .missingSubscriptionType:
                record.recordType = "behavior"
                fixedCount += 1
                print("✅ 修复积分记录类型缺失")

            default:
                break
            }
        }

        return fixedCount
    }

    /**
     * 修复关联关系问题
     */
    private func fixRelationshipIssues(_ issues: [DataIntegrityIssue]) -> Int {
        var fixedCount = 0

        for issue in issues {
            switch issue.type {
            case .invalidRelationship:
                if let user = issue.entity as? User, user.members?.count == 0 {
                    // 为没有成员的用户创建一个默认成员
                    let member = Member(context: viewContext)
                    member.id = UUID()
                    member.name = user.nickname ?? "默认成员"
                    member.role = "other"
                    member.currentPoints = 0
                    member.createdAt = Date()
                    member.updatedAt = Date()
                    member.user = user
                    fixedCount += 1
                    print("✅ 为用户创建默认成员")
                }

            default:
                break
            }
        }

        return fixedCount
    }

    /**
     * 修复重复数据问题
     */
    private func fixDuplicateDataIssues(_ issues: [DataIntegrityIssue]) -> Int {
        var fixedCount = 0

        for issue in issues {
            if issue.description.contains("重复的积分记录") {
                // 对于重复的积分记录，这里可以实现去重逻辑
                // 暂时只记录，不自动删除
                print("⚠️ 发现重复积分记录，需要手动处理")
            }
        }

        return fixedCount
    }

    // MARK: - Helper Methods
    
    /**
     * 更新最后同步时间
     */
    private func updateLastSyncDate() {
        UserDefaults.standard.set(lastSyncDate, forKey: "last_cloudkit_sync_date")
    }

    // MARK: - Reset Methods

    /**
     * 重置同步状态
     * 用于清除所有数据后的状态重置
     */
    func resetSyncStatus() {
        print("🔄 重置CoreDataManager同步状态...")

        // 重置发布的属性
        cloudKitStatus = .notStarted
        isSyncing = false
        lastSyncDate = nil
        syncError = nil

        // 取消所有观察者
        cancellables.removeAll()

        // 重新设置通知观察者
        setupCloudKitNotifications()

        // 重新设置初始状态
        setupInitialState()

        print("✅ CoreDataManager同步状态重置完成")
    }
}

// MARK: - CloudKit Sync Status Enum

enum CloudKitSyncStatus {
    case notStarted
    case syncInProgress
    case syncCompleted
    case syncFailed
    
    var displayText: String {
        switch self {
        case .notStarted:
            return "未开始"
        case .syncInProgress:
            return "同步中"
        case .syncCompleted:
            return "同步完成"
        case .syncFailed:
            return "同步失败"
        }
    }
    
    var iconName: String {
        switch self {
        case .notStarted:
            return "icloud"
        case .syncInProgress:
            return "icloud.and.arrow.up"
        case .syncCompleted:
            return "icloud.and.arrow.up.fill"
        case .syncFailed:
            return "icloud.slash"
        }
    }
}
