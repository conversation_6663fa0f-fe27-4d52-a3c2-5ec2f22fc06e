//
//  DataProtectionManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import Combine
import CloudKit

/**
 * 数据保护管理器
 * 
 * 统一管理所有数据保护功能，包括：
 * - 多层存储架构
 * - 数据一致性检查
 * - 数据诊断工具
 * - CloudKit同步重试
 * - 多设备同步测试
 * - 数据备份和恢复
 */
@MainActor
class DataProtectionManager: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = DataProtectionManager()
    
    // MARK: - Published Properties
    @Published var isHealthy: Bool = true
    @Published var lastHealthCheck: Date?
    @Published var syncStatus: String = "正常"
    @Published var dataIssuesCount: Int = 0
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let persistenceController = PersistenceController.shared
    private let diagnosticTool = DataDiagnosticTool.shared
    private let syncSimulator = MultiDeviceSyncSimulator()
    private let trialManager = TrialManager.shared
    
    private var cancellables = Set<AnyCancellable>()
    private var healthCheckTimer: Timer?
    
    // MARK: - Initialization
    private init() {
        setupHealthMonitoring()
        performInitialHealthCheck()
    }
    
    // MARK: - 健康监控
    
    /**
     * 设置健康监控
     */
    private func setupHealthMonitoring() {
        // 每30分钟执行一次健康检查
        healthCheckTimer = Timer.scheduledTimer(withTimeInterval: 1800, repeats: true) { _ in
            Task {
                await self.performHealthCheck()
            }
        }
        
        // 监听CloudKit同步完成通知
        NotificationCenter.default.publisher(for: NSNotification.Name("CloudKitSyncCompleted"))
            .sink { _ in
                Task {
                    await self.performQuickHealthCheck()
                }
            }
            .store(in: &cancellables)
    }
    
    /**
     * 执行初始健康检查
     */
    private func performInitialHealthCheck() {
        Task {
            await performHealthCheck()
        }
    }
    
    /**
     * 执行完整健康检查
     */
    func performHealthCheck() async {
        print("🏥 开始数据健康检查...")
        
        let report = diagnosticTool.performFullDiagnostic()
        
        await MainActor.run {
            self.lastHealthCheck = Date()
            self.dataIssuesCount = report.consistencyIssues.count
            self.isHealthy = report.consistencyIssues.filter { 
                $0.severity == .critical || $0.severity == .high 
            }.isEmpty
            
            if !self.isHealthy {
                print("⚠️ 发现数据健康问题，建议执行修复")
            } else {
                print("✅ 数据健康状态良好")
            }
        }
    }
    
    /**
     * 执行快速健康检查
     */
    func performQuickHealthCheck() async {
        let isHealthy = diagnosticTool.performQuickHealthCheck()
        
        await MainActor.run {
            self.isHealthy = isHealthy
            self.lastHealthCheck = Date()
        }
    }
    
    // MARK: - 数据修复
    
    /**
     * 自动修复数据问题
     */
    func autoFixDataIssues() async -> Bool {
        print("🔧 开始自动修复数据问题...")
        
        let success = diagnosticTool.autoFixIssues()
        
        if success {
            // 修复后重新检查健康状态
            await performHealthCheck()
        }
        
        return success
    }
    
    /**
     * 强制数据一致性检查
     */
    func forceDataConsistencyCheck() {
        print("🔍 强制执行数据一致性检查...")
        coreDataManager.triggerDataConsistencyCheck()
        
        Task {
            await performHealthCheck()
        }
    }
    
    // MARK: - 同步管理
    
    /**
     * 强制CloudKit同步
     */
    func forceCloudKitSync() {
        print("☁️ 强制CloudKit同步...")
        persistenceController.forceCloudKitSync()
        
        syncStatus = "同步中..."
        
        // 5秒后更新状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            self.syncStatus = "已同步"
        }
    }
    
    /**
     * 测试多设备同步
     */
    func testMultiDeviceSync() async -> MultiDeviceSyncSimulator.SyncTestReport {
        print("📱 测试多设备同步...")
        
        syncStatus = "测试中..."
        
        let report = syncSimulator.runFullSyncTest()
        
        await MainActor.run {
            self.syncStatus = report.successRate > 0.8 ? "同步正常" : "同步异常"
        }
        
        return report
    }
    
    /**
     * 快速同步测试
     */
    func quickSyncTest() async -> Bool {
        let success = syncSimulator.runQuickSyncTest()
        
        await MainActor.run {
            self.syncStatus = success ? "同步正常" : "同步异常"
        }
        
        return success
    }
    
    // MARK: - 数据备份和恢复
    
    /**
     * 创建数据备份
     */
    func createDataBackup() async -> Bool {
        print("💾 创建数据备份...")
        
        // 触发CloudKit同步以确保数据最新
        persistenceController.forceCloudKitSync()
        
        // 等待同步完成
        try? await Task.sleep(nanoseconds: 3_000_000_000)
        
        // 记录备份时间
        UserDefaults.standard.set(Date(), forKey: "last_data_backup_date")
        
        print("✅ 数据备份完成")
        return true
    }
    
    /**
     * 恢复数据
     */
    func restoreData() async -> Bool {
        print("🔄 开始数据恢复...")
        
        // 强制从CloudKit拉取最新数据
        persistenceController.forceCloudKitSync()
        
        // 等待同步完成
        try? await Task.sleep(nanoseconds: 5_000_000_000)
        
        // 执行数据一致性检查
        coreDataManager.triggerDataConsistencyCheck()
        
        // 重新检查健康状态
        await performHealthCheck()
        
        print("✅ 数据恢复完成")
        return true
    }
    
    // MARK: - 试用状态保护
    
    /**
     * 刷新试用状态
     */
    func refreshTrialStatus() {
        trialManager.refreshTrialStatus()
    }

    /**
     * 强制试用状态同步
     */
    func forceTrialSync() {
        // TrialManager的forceCloudSync是私有方法，这里使用公共方法
        Task {
            trialManager.refreshTrialStatus()
        }
    }
    
    // MARK: - 报告生成
    
    /**
     * 生成完整的数据保护报告
     */
    func generateProtectionReport() async -> String {
        let diagnosticReport = diagnosticTool.performFullDiagnostic()
        let syncReport = await testMultiDeviceSync()
        
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .medium

        let report = """
        📊 数据保护状态报告
        生成时间: \(formatter.string(from: Date()))

        🏥 健康状态:
        • 整体健康: \(isHealthy ? "良好" : "异常")
        • 数据问题: \(dataIssuesCount) 个
        • 最后检查: \(lastHealthCheck?.description ?? "未知")

        ☁️ 同步状态:
        • 当前状态: \(syncStatus)
        • 同步成功率: \(String(format: "%.1f", syncReport.successRate * 100))%

        📋 诊断摘要:
        \(diagnosticTool.generateReportText(diagnosticReport))

        📱 同步测试:
        \(syncSimulator.generateReportText(syncReport))

        💡 建议:
        """
        
        var recommendations = [String]()
        
        if !isHealthy {
            recommendations.append("• 立即执行数据修复")
        }
        
        if syncReport.successRate < 0.8 {
            recommendations.append("• 检查网络连接和iCloud状态")
        }
        
        if dataIssuesCount > 0 {
            recommendations.append("• 运行数据一致性检查")
        }
        
        if recommendations.isEmpty {
            recommendations.append("• 数据保护状态良好，继续保持")
        }
        
        return report + recommendations.joined(separator: "\n")
    }
    
    // MARK: - 清理
    
    deinit {
        healthCheckTimer?.invalidate()
    }
}
