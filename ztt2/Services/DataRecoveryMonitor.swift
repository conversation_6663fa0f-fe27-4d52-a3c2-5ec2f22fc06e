//
//  DataRecoveryMonitor.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import Combine
import CoreData
import UIKit

/**
 * 数据恢复监控器
 * 在应用运行时持续监控数据状态，自动触发恢复机制
 */
@MainActor
class DataRecoveryMonitor: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataRecoveryMonitor()
    
    // MARK: - Published Properties
    @Published var isMonitoring: Bool = false
    @Published var lastCheckDate: Date?
    @Published var recoveryTriggeredCount: Int = 0
    @Published var criticalIssuesDetected: Int = 0
    
    // MARK: - Private Properties
    private let multiLayerStorage = MultiLayerStorageManager.shared
    private let diagnosticTool = DataDiagnosticTool.shared
    private let dataManager = DataManager.shared
    private let trialManager = TrialManager.shared
    
    private var monitoringTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Configuration
    private let monitoringInterval: TimeInterval = 300 // 5分钟检查一次
    private let criticalCheckInterval: TimeInterval = 60 // 1分钟检查严重问题
    private let maxAutoRecoveryAttempts = 3 // 最大自动恢复尝试次数
    
    // MARK: - Initialization
    private init() {
        setupNotificationObservers()
        loadMonitoringState()
    }
    
    deinit {
        Task { @MainActor in
            stopMonitoring()
        }
    }
    
    // MARK: - Public Methods
    
    /**
     * 开始监控
     */
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        print("🔍 开始数据恢复监控...")
        isMonitoring = true
        
        // 立即执行一次检查
        Task {
            await performDataHealthCheck()
        }
        
        // 设置定期检查定时器
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: monitoringInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performDataHealthCheck()
            }
        }
        
        // 设置严重问题检查定时器
        Timer.scheduledTimer(withTimeInterval: criticalCheckInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performCriticalIssueCheck()
            }
        }
        
        saveMonitoringState()
        print("✅ 数据恢复监控已启动")
    }
    
    /**
     * 停止监控
     */
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        print("⏸️ 停止数据恢复监控...")
        isMonitoring = false
        
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        saveMonitoringState()
        print("✅ 数据恢复监控已停止")
    }
    
    /**
     * 手动触发数据健康检查
     */
    func triggerManualCheck() async {
        print("🔄 手动触发数据健康检查...")
        await performDataHealthCheck()
    }
    
    /**
     * 重置监控状态
     */
    func resetMonitoringState() {
        recoveryTriggeredCount = 0
        criticalIssuesDetected = 0
        lastCheckDate = nil
        saveMonitoringState()
        print("🔄 监控状态已重置")
    }
    
    // MARK: - Private Methods
    
    /**
     * 执行数据健康检查
     */
    private func performDataHealthCheck() async {
        lastCheckDate = Date()
        
        print("🔍 执行数据健康检查...")
        
        // 1. 检查数据一致性
        let inconsistencies = multiLayerStorage.checkDataConsistency()
        
        // 2. 执行快速诊断
        let report = await diagnosticTool.performFullDiagnostic()
        
        // 3. 检查关键数据完整性
        let criticalIssues = report.issuesFound.filter { $0.severity == .critical }
        criticalIssuesDetected = criticalIssues.count
        
        // 4. 如果发现问题，触发恢复
        if !inconsistencies.isEmpty || !criticalIssues.isEmpty {
            print("⚠️ 发现数据问题，触发自动恢复...")
            await triggerAutoRecovery(issues: criticalIssues, inconsistencies: inconsistencies)
        } else {
            print("✅ 数据健康检查通过")
        }
        
        saveMonitoringState()
    }
    
    /**
     * 执行严重问题检查
     */
    private func performCriticalIssueCheck() async {
        // 检查关键数据是否存在
        let hasUser = dataManager.currentUser != nil
        let hasValidSubscription = dataManager.currentUser?.subscription != nil
        
        if !hasUser || !hasValidSubscription {
            print("🚨 检测到严重数据问题，立即触发恢复...")
            await triggerEmergencyRecovery()
        }
    }
    
    /**
     * 触发自动恢复
     */
    private func triggerAutoRecovery(issues: [DataIntegrityIssue], inconsistencies: [String]) async {
        guard recoveryTriggeredCount < maxAutoRecoveryAttempts else {
            print("⚠️ 已达到最大自动恢复尝试次数，停止自动恢复")
            return
        }
        
        recoveryTriggeredCount += 1
        
        print("🔧 开始自动数据恢复 (第\(recoveryTriggeredCount)次)...")
        
        // 1. 修复数据不一致
        if !inconsistencies.isEmpty {
            await multiLayerStorage.repairDataInconsistencies()
        }
        
        // 2. 修复严重问题
        if !issues.isEmpty {
            let _ = await diagnosticTool.performDataRepair()
        }
        
        // 3. 触发试用数据恢复
        await trialManager.triggerDataRecovery()
        
        print("✅ 自动数据恢复完成")
        
        // 发送恢复完成通知
        NotificationCenter.default.post(
            name: NSNotification.Name("DataRecoveryCompleted"),
            object: nil,
            userInfo: [
                "recoveryCount": recoveryTriggeredCount,
                "issuesFixed": issues.count,
                "inconsistenciesFixed": inconsistencies.count
            ]
        )
    }
    
    /**
     * 触发紧急恢复
     */
    private func triggerEmergencyRecovery() async {
        print("🚨 执行紧急数据恢复...")
        
        // 执行完整的数据恢复流程
        await multiLayerStorage.performDataRecovery()
        await diagnosticTool.performDataRepair()
        await trialManager.triggerDataRecovery()
        
        // 发送紧急恢复通知
        NotificationCenter.default.post(
            name: NSNotification.Name("EmergencyDataRecoveryCompleted"),
            object: nil
        )
        
        print("✅ 紧急数据恢复完成")
    }
    
    /**
     * 设置通知观察者
     */
    private func setupNotificationObservers() {
        // 监听应用进入前台
        NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.performDataHealthCheck()
            }
        }
        
        // 监听CloudKit同步完成
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CloudKitSyncCompleted"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.performDataHealthCheck()
            }
        }
        
        // 监听数据变更
        NotificationCenter.default.addObserver(
            forName: .NSManagedObjectContextDidSave,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            // 延迟检查，避免频繁触发
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                Task { @MainActor in
                    await self?.performCriticalIssueCheck()
                }
            }
        }
    }
    
    /**
     * 加载监控状态
     */
    private func loadMonitoringState() {
        let userDefaults = UserDefaults.standard
        
        recoveryTriggeredCount = userDefaults.integer(forKey: "data_recovery_triggered_count")
        criticalIssuesDetected = userDefaults.integer(forKey: "critical_issues_detected")
        
        if let lastCheck = userDefaults.object(forKey: "last_data_check_date") as? Date {
            lastCheckDate = lastCheck
        }
        
        // 默认启动监控
        if userDefaults.object(forKey: "data_monitoring_enabled") == nil {
            userDefaults.set(true, forKey: "data_monitoring_enabled")
        }
        
        let monitoringEnabled = userDefaults.bool(forKey: "data_monitoring_enabled")
        if monitoringEnabled {
            startMonitoring()
        }
    }
    
    /**
     * 保存监控状态
     */
    private func saveMonitoringState() {
        let userDefaults = UserDefaults.standard
        
        userDefaults.set(isMonitoring, forKey: "data_monitoring_enabled")
        userDefaults.set(recoveryTriggeredCount, forKey: "data_recovery_triggered_count")
        userDefaults.set(criticalIssuesDetected, forKey: "critical_issues_detected")
        
        if let lastCheck = lastCheckDate {
            userDefaults.set(lastCheck, forKey: "last_data_check_date")
        }
        
        userDefaults.synchronize()
    }
}

// MARK: - Notification Names Extension

extension NSNotification.Name {
    static let dataRecoveryCompleted = NSNotification.Name("DataRecoveryCompleted")
    static let emergencyDataRecoveryCompleted = NSNotification.Name("EmergencyDataRecoveryCompleted")
}
