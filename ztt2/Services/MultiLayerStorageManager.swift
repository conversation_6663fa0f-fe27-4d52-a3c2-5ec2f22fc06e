//
//  MultiLayerStorageManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import CoreData
import Combine

/**
 * 多层存储管理器
 * 实现三层存储架构：CoreData > NSUbiquitousKeyValueStore > UserDefaults
 * 确保数据在应用卸载重装后能够恢复
 */
@MainActor
class MultiLayerStorageManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = MultiLayerStorageManager()
    
    // MARK: - Storage Layers
    private let coreDataManager = CoreDataManager.shared
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Published Properties
    @Published var lastRecoveryDate: Date?
    @Published var recoveryStatus: RecoveryStatus = .idle
    @Published var recoveredDataCount: Int = 0
    
    // MARK: - Types
    enum StorageLayer: String, CaseIterable {
        case coreData = "CoreData"
        case ubiquitousStore = "NSUbiquitousKeyValueStore"
        case userDefaults = "UserDefaults"
        
        var priority: Int {
            switch self {
            case .coreData: return 3
            case .ubiquitousStore: return 2
            case .userDefaults: return 1
            }
        }
    }
    
    enum RecoveryStatus {
        case idle
        case recovering
        case completed(recoveredItems: Int)
        case failed(error: Error)
    }
    
    // MARK: - Storage Keys
    struct StorageKeys {
        // 试用相关
        static let hasReceivedTrial = "hasReceivedTrial"
        static let trialExpirationDate = "trialExpirationDate"
        
        // 用户偏好
        static let appLanguage = "app_language"
        static let notificationsEnabled = "notifications_enabled"
        static let soundEnabled = "sound_enabled"
        static let hapticFeedbackEnabled = "haptic_feedback_enabled"
        
        // 同步状态
        static let lastSyncDate = "last_sync_date"
        static let iCloudSyncEnabled = "icloud_sync_enabled"
        
        // 数据恢复
        static let lastRecoveryDate = "last_recovery_date"
        static let dataRecoveryCount = "data_recovery_count"
    }
    
    // MARK: - Initialization
    private init() {
        setupUbiquitousStoreObserver()
        loadRecoveryStatus()
    }
    
    // MARK: - Public Methods
    
    /**
     * 存储数据到所有层
     */
    func store<T>(_ value: T, forKey key: String, syncToCoreData: Bool = false) {
        // 存储到UserDefaults（最低优先级）
        userDefaults.set(value, forKey: key)
        userDefaults.synchronize()
        
        // 存储到NSUbiquitousKeyValueStore（中等优先级）
        ubiquitousStore.set(value, forKey: key)
        ubiquitousStore.synchronize()
        
        // 如果需要，存储到CoreData（最高优先级）
        if syncToCoreData {
            storeToCoreData(value, forKey: key)
        }
        
        print("💾 数据已存储到所有层: \(key) = \(value)")
    }
    
    /**
     * 从多层存储中恢复数据
     */
    func retrieve<T>(_ type: T.Type, forKey key: String, defaultValue: T? = nil) -> T? {
        // 按优先级顺序检查各存储层
        for layer in StorageLayer.allCases.sorted(by: { $0.priority > $1.priority }) {
            if let value = retrieveFromLayer(layer, type: type, key: key) {
                print("📱 从\(layer.rawValue)恢复数据: \(key) = \(value)")
                
                // 同步到其他存储层
                syncToOtherLayers(value, forKey: key, sourceLayer: layer)
                return value
            }
        }
        
        print("⚠️ 未找到数据: \(key)，使用默认值: \(defaultValue ?? "nil")")
        return defaultValue
    }
    
    /**
     * 执行完整的数据恢复
     */
    func performDataRecovery() async {
        await MainActor.run {
            recoveryStatus = .recovering
            recoveredDataCount = 0
        }
        
        print("🔄 开始执行数据恢复...")
        
        do {
            var recoveredCount = 0
            
            // 恢复试用状态
            if let hasReceived: Bool = retrieve(Bool.self, forKey: StorageKeys.hasReceivedTrial) {
                recoveredCount += 1
            }
            
            if let expirationDate: Date = retrieve(Date.self, forKey: StorageKeys.trialExpirationDate) {
                recoveredCount += 1
            }
            
            // 恢复用户偏好
            if let language: String = retrieve(String.self, forKey: StorageKeys.appLanguage) {
                recoveredCount += 1
            }
            
            if let notificationsEnabled: Bool = retrieve(Bool.self, forKey: StorageKeys.notificationsEnabled) {
                recoveredCount += 1
            }
            
            if let soundEnabled: Bool = retrieve(Bool.self, forKey: StorageKeys.soundEnabled) {
                recoveredCount += 1
            }
            
            if let hapticEnabled: Bool = retrieve(Bool.self, forKey: StorageKeys.hapticFeedbackEnabled) {
                recoveredCount += 1
            }
            
            // 恢复同步状态
            if let syncEnabled: Bool = retrieve(Bool.self, forKey: StorageKeys.iCloudSyncEnabled) {
                recoveredCount += 1
            }
            
            if let lastSync: Date = retrieve(Date.self, forKey: StorageKeys.lastSyncDate) {
                recoveredCount += 1
            }
            
            await MainActor.run {
                self.recoveredDataCount = recoveredCount
                self.lastRecoveryDate = Date()
                self.recoveryStatus = .completed(recoveredItems: recoveredCount)
                
                // 保存恢复状态
                self.userDefaults.set(Date(), forKey: StorageKeys.lastRecoveryDate)
                self.userDefaults.set(recoveredCount, forKey: StorageKeys.dataRecoveryCount)
                self.userDefaults.synchronize()
            }
            
            print("✅ 数据恢复完成，恢复了 \(recoveredCount) 项数据")
            
        } catch {
            await MainActor.run {
                self.recoveryStatus = .failed(error: error)
            }
            print("❌ 数据恢复失败: \(error)")
        }
    }
    
    /**
     * 检查数据一致性
     */
    func checkDataConsistency() -> [String] {
        var inconsistencies: [String] = []
        
        // 检查关键数据在各层的一致性
        let keysToCheck = [
            StorageKeys.hasReceivedTrial,
            StorageKeys.trialExpirationDate,
            StorageKeys.appLanguage,
            StorageKeys.iCloudSyncEnabled
        ]
        
        for key in keysToCheck {
            let coreDataValue = retrieveFromCoreData(key: key)
            let cloudValue = ubiquitousStore.object(forKey: key)
            let localValue = userDefaults.object(forKey: key)
            
            // 检查是否存在不一致
            if !areValuesConsistent(coreDataValue, cloudValue, localValue) {
                inconsistencies.append("数据不一致: \(key)")
            }
        }
        
        return inconsistencies
    }
    
    /**
     * 修复数据不一致问题
     */
    func repairDataInconsistencies() async {
        let inconsistencies = checkDataConsistency()
        
        if inconsistencies.isEmpty {
            print("✅ 数据一致性检查通过")
            return
        }
        
        print("🔧 发现 \(inconsistencies.count) 个数据不一致问题，开始修复...")
        
        // 执行数据恢复来修复不一致问题
        await performDataRecovery()
        
        print("✅ 数据不一致问题修复完成")
    }
    
    // MARK: - Private Methods
    
    private func setupUbiquitousStoreObserver() {
        NotificationCenter.default.addObserver(
            forName: NSUbiquitousKeyValueStore.didChangeExternallyNotification,
            object: ubiquitousStore,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleExternalStoreChange()
            }
        }
    }
    
    private func loadRecoveryStatus() {
        if let lastRecovery = userDefaults.object(forKey: StorageKeys.lastRecoveryDate) as? Date {
            lastRecoveryDate = lastRecovery
        }
        
        recoveredDataCount = userDefaults.integer(forKey: StorageKeys.dataRecoveryCount)
    }
    
    private func handleExternalStoreChange() {
        print("🔄 检测到外部存储变化，触发数据同步...")
        
        Task {
            await performDataRecovery()
        }
    }
    
    private func retrieveFromLayer<T>(_ layer: StorageLayer, type: T.Type, key: String) -> T? {
        switch layer {
        case .coreData:
            return retrieveFromCoreData(key: key) as? T
        case .ubiquitousStore:
            return ubiquitousStore.object(forKey: key) as? T
        case .userDefaults:
            return userDefaults.object(forKey: key) as? T
        }
    }
    
    private func retrieveFromCoreData(key: String) -> Any? {
        // 这里实现从CoreData获取数据的逻辑
        // 根据key的类型从相应的实体中获取数据
        switch key {
        case StorageKeys.hasReceivedTrial:
            return coreDataManager.getCurrentUser()?.subscription?.hasReceivedTrial
        case StorageKeys.trialExpirationDate:
            return coreDataManager.getCurrentUser()?.subscription?.expirationDate
        default:
            return nil
        }
    }
    
    private func storeToCoreData<T>(_ value: T, forKey key: String) {
        // 这里实现存储到CoreData的逻辑
        switch key {
        case StorageKeys.hasReceivedTrial:
            if let boolValue = value as? Bool {
                coreDataManager.getCurrentUser()?.subscription?.hasReceivedTrial = boolValue
                coreDataManager.save()
            }
        case StorageKeys.trialExpirationDate:
            if let dateValue = value as? Date {
                coreDataManager.getCurrentUser()?.subscription?.expirationDate = dateValue
                coreDataManager.save()
            }
        default:
            break
        }
    }
    
    private func syncToOtherLayers<T>(_ value: T, forKey key: String, sourceLayer: StorageLayer) {
        // 同步到其他存储层
        for layer in StorageLayer.allCases {
            if layer != sourceLayer {
                switch layer {
                case .coreData:
                    storeToCoreData(value, forKey: key)
                case .ubiquitousStore:
                    ubiquitousStore.set(value, forKey: key)
                    ubiquitousStore.synchronize()
                case .userDefaults:
                    userDefaults.set(value, forKey: key)
                    userDefaults.synchronize()
                }
            }
        }
    }
    
    private func areValuesConsistent(_ value1: Any?, _ value2: Any?, _ value3: Any?) -> Bool {
        // 简单的一致性检查
        // 如果有值存在，检查它们是否相等
        let nonNilValues = [value1, value2, value3].compactMap { $0 }
        
        if nonNilValues.isEmpty {
            return true // 都是nil，认为是一致的
        }
        
        // 检查所有非nil值是否相等
        let firstValue = nonNilValues.first!
        return nonNilValues.allSatisfy { areEqual($0, firstValue) }
    }
    
    private func areEqual(_ value1: Any, _ value2: Any) -> Bool {
        // 类型安全的相等性检查
        if let date1 = value1 as? Date, let date2 = value2 as? Date {
            return abs(date1.timeIntervalSince(date2)) < 1.0 // 允许1秒误差
        }
        
        if let bool1 = value1 as? Bool, let bool2 = value2 as? Bool {
            return bool1 == bool2
        }
        
        if let string1 = value1 as? String, let string2 = value2 as? String {
            return string1 == string2
        }
        
        return false
    }
}
