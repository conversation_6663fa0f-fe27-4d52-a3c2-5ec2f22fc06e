//
//  DataProtectionTests.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation

/**
 * 数据保护功能测试类
 * 
 * 提供简单的测试方法来验证数据保护功能是否正常工作
 */
class DataProtectionTests {
    
    // MARK: - Shared Instance
    static let shared = DataProtectionTests()
    
    // MARK: - Private Properties
    private let protectionManager = DataProtectionManager.shared
    private let diagnosticTool = DataDiagnosticTool.shared
    private let syncSimulator = MultiDeviceSyncSimulator()
    
    private init() {}
    
    // MARK: - 测试方法
    
    /**
     * 运行所有测试
     */
    func runAllTests() async -> TestSummary {
        print("🧪 开始数据保护功能测试...")
        
        var results: [TestResult] = []
        
        // 测试1: 健康检查
        results.append(await testHealthCheck())
        
        // 测试2: 数据诊断
        results.append(testDataDiagnostic())
        
        // 测试3: 数据一致性检查
        results.append(testDataConsistency())
        
        // 测试4: 快速同步测试
        results.append(await testQuickSync())
        
        let passedCount = results.filter { $0.passed }.count
        let summary = TestSummary(
            totalTests: results.count,
            passedTests: passedCount,
            failedTests: results.count - passedCount,
            results: results
        )
        
        print("✅ 测试完成，通过率: \(String(format: "%.1f", summary.successRate * 100))%")
        
        return summary
    }
    
    /**
     * 测试健康检查功能
     */
    private func testHealthCheck() async -> TestResult {
        print("🏥 测试健康检查功能...")
        
        do {
            await protectionManager.performHealthCheck()
            
            let isHealthy = protectionManager.isHealthy
            let hasLastCheck = protectionManager.lastHealthCheck != nil
            
            let success = hasLastCheck
            
            return TestResult(
                name: "健康检查",
                passed: success,
                message: success ? "健康检查功能正常" : "健康检查功能异常",
                details: "健康状态: \(isHealthy ? "良好" : "异常"), 最后检查: \(hasLastCheck ? "有记录" : "无记录")"
            )
        } catch {
            return TestResult(
                name: "健康检查",
                passed: false,
                message: "健康检查测试失败",
                details: "错误: \(error.localizedDescription)"
            )
        }
    }
    
    /**
     * 测试数据诊断功能
     */
    private func testDataDiagnostic() -> TestResult {
        print("🔍 测试数据诊断功能...")
        
        let report = diagnosticTool.performFullDiagnostic()
        
        let hasUserAnalysis = report.userAnalysis.totalUsers >= 0
        let hasMemberAnalysis = report.memberAnalysis.totalMembers >= 0
        let hasPointRecordAnalysis = report.pointRecordAnalysis.totalRecords >= 0
        let hasRecommendations = !report.recommendations.isEmpty
        
        let success = hasUserAnalysis && hasMemberAnalysis && hasPointRecordAnalysis && hasRecommendations
        
        return TestResult(
            name: "数据诊断",
            passed: success,
            message: success ? "数据诊断功能正常" : "数据诊断功能异常",
            details: "用户: \(report.userAnalysis.totalUsers), 成员: \(report.memberAnalysis.totalMembers), 积分记录: \(report.pointRecordAnalysis.totalRecords), 问题: \(report.consistencyIssues.count)"
        )
    }
    
    /**
     * 测试数据一致性检查
     */
    private func testDataConsistency() -> TestResult {
        print("🔧 测试数据一致性检查...")
        
        protectionManager.forceDataConsistencyCheck()
        
        // 等待检查完成
        Thread.sleep(forTimeInterval: 1.0)
        
        // 简单验证：如果没有崩溃就认为成功
        let success = true
        
        return TestResult(
            name: "数据一致性检查",
            passed: success,
            message: success ? "数据一致性检查功能正常" : "数据一致性检查功能异常",
            details: "一致性检查已执行"
        )
    }
    
    /**
     * 测试快速同步
     */
    private func testQuickSync() async -> TestResult {
        print("⚡ 测试快速同步功能...")
        
        let success = await protectionManager.quickSyncTest()
        
        return TestResult(
            name: "快速同步测试",
            passed: success,
            message: success ? "快速同步功能正常" : "快速同步功能异常",
            details: "同步状态: \(protectionManager.syncStatus)"
        )
    }
    
    /**
     * 生成测试报告
     */
    func generateTestReport(_ summary: TestSummary) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .medium
        
        var report = """
        📊 数据保护功能测试报告
        生成时间: \(formatter.string(from: Date()))
        
        📈 测试统计:
        • 总测试数: \(summary.totalTests)
        • 通过测试: \(summary.passedTests)
        • 失败测试: \(summary.failedTests)
        • 成功率: \(String(format: "%.1f", summary.successRate * 100))%
        
        📋 详细结果:
        """
        
        for (index, result) in summary.results.enumerated() {
            let status = result.passed ? "✅" : "❌"
            report += "\n\(index + 1). \(status) \(result.name): \(result.message)"
            if !result.details.isEmpty {
                report += "\n   详情: \(result.details)"
            }
        }
        
        if summary.failedTests > 0 {
            report += "\n\n⚠️ 建议:\n"
            report += "• 检查失败的测试项目\n"
            report += "• 验证数据完整性\n"
            report += "• 重启应用后重新测试"
        } else {
            report += "\n\n🎉 所有测试通过！数据保护功能正常。"
        }
        
        return report
    }
    
    // MARK: - 数据结构
    
    struct TestResult {
        let name: String
        let passed: Bool
        let message: String
        let details: String
    }
    
    struct TestSummary {
        let totalTests: Int
        let passedTests: Int
        let failedTests: Int
        let results: [TestResult]
        
        var successRate: Double {
            return totalTests > 0 ? Double(passedTests) / Double(totalTests) : 0.0
        }
    }
}

// MARK: - 便捷测试方法

extension DataProtectionTests {
    
    /**
     * 快速测试（仅基本功能）
     */
    func quickTest() async -> Bool {
        print("⚡ 执行快速测试...")
        
        let healthResult = await testHealthCheck()
        let diagnosticResult = testDataDiagnostic()
        
        let success = healthResult.passed && diagnosticResult.passed
        print("快速测试结果: \(success ? "通过" : "失败")")
        
        return success
    }
    
    /**
     * 打印当前数据保护状态
     */
    func printCurrentStatus() {
        print("""
        📊 当前数据保护状态:
        • 健康状态: \(protectionManager.isHealthy ? "良好" : "异常")
        • 数据问题: \(protectionManager.dataIssuesCount) 个
        • 同步状态: \(protectionManager.syncStatus)
        • 最后检查: \(protectionManager.lastHealthCheck?.description ?? "未知")
        """)
    }
}
