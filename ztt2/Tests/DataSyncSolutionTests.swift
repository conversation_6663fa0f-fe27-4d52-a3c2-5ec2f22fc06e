//
//  DataSyncSolutionTests.swift
//  ztt2Tests
//
//  Created by AI Assistant on 2025/8/5.
//

import XCTest

/**
 * 数据同步解决方案测试
 * 验证多层存储、数据恢复、一致性检查等功能
 */
class DataSyncSolutionTests: XCTestCase {

    override func setUp() {
        super.setUp()
    }

    override func tearDown() {
        super.tearDown()
        // 清理测试数据
        UserDefaults.standard.removeObject(forKey: "test_key")
        NSUbiquitousKeyValueStore.default.removeObject(forKey: "test_key")
    }
    
    // MARK: - 多层存储测试

    /**
     * 测试多层存储功能
     */
    func testMultiLayerStorage() async {
        let testKey = "test_key"
        let testValue = "test_value"

        await MainActor.run {
            let multiLayerStorage = MultiLayerStorageManager.shared

            // 存储数据
            multiLayerStorage.store(testValue, forKey: testKey)

            // 验证数据能够从多层存储中恢复
            let retrievedValue: String? = multiLayerStorage.retrieve(String.self, forKey: testKey)
            XCTAssertEqual(retrievedValue, testValue, "多层存储应该能够正确存储和恢复数据")
        }

        // 验证数据存在于UserDefaults中
        let userDefaultsValue = UserDefaults.standard.string(forKey: testKey)
        XCTAssertEqual(userDefaultsValue, testValue, "数据应该存储在UserDefaults中")

        // 验证数据存在于NSUbiquitousKeyValueStore中
        let ubiquitousValue = NSUbiquitousKeyValueStore.default.string(forKey: testKey)
        XCTAssertEqual(ubiquitousValue, testValue, "数据应该存储在NSUbiquitousKeyValueStore中")
    }
    
    /**
     * 测试数据恢复优先级
     */
    func testDataRecoveryPriority() async {
        let testKey = "priority_test_key"

        // 在不同存储层设置不同的值
        UserDefaults.standard.set("userdefaults_value", forKey: testKey)
        NSUbiquitousKeyValueStore.default.set("ubiquitous_value", forKey: testKey)

        await MainActor.run {
            let multiLayerStorage = MultiLayerStorageManager.shared

            // 验证优先级：NSUbiquitousKeyValueStore > UserDefaults
            let retrievedValue: String? = multiLayerStorage.retrieve(String.self, forKey: testKey)
            XCTAssertEqual(retrievedValue, "ubiquitous_value", "应该优先使用NSUbiquitousKeyValueStore中的数据")
        }

        // 清理
        UserDefaults.standard.removeObject(forKey: testKey)
        NSUbiquitousKeyValueStore.default.removeObject(forKey: testKey)
    }
    
    /**
     * 测试数据一致性检查
     */
    func testDataConsistencyCheck() async {
        await MainActor.run {
            let multiLayerStorage = MultiLayerStorageManager.shared
            let inconsistencies = multiLayerStorage.checkDataConsistency()

            // 验证一致性检查能够正常运行
            XCTAssertTrue(inconsistencies.count >= 0, "数据一致性检查应该能够正常运行")

            print("发现 \(inconsistencies.count) 个数据不一致问题")
            for inconsistency in inconsistencies {
                print("- \(inconsistency)")
            }
        }
    }
    
    // MARK: - 数据诊断测试
    
    /**
     * 测试数据诊断功能
     */
    func testDataDiagnostic() async {
        let diagnosticTool = await MainActor.run { DataDiagnosticTool.shared }
        let report = await diagnosticTool.performFullDiagnostic()

        // 验证诊断报告的基本结构
        XCTAssertNotNil(report, "应该能够生成诊断报告")
        XCTAssertTrue(report.totalIssues >= 0, "问题总数应该大于等于0")
        XCTAssertTrue(report.userAnalysis.totalUsers >= 0, "用户总数应该大于等于0")
        XCTAssertTrue(report.memberAnalysis.totalMembers >= 0, "成员总数应该大于等于0")
        XCTAssertTrue(report.pointRecordAnalysis.totalRecords >= 0, "积分记录总数应该大于等于0")

        print("诊断报告:")
        print("- 总问题数: \(report.totalIssues)")
        print("- 严重问题: \(report.criticalIssues)")
        print("- 用户数: \(report.userAnalysis.totalUsers)")
        print("- 成员数: \(report.memberAnalysis.totalMembers)")
        print("- 积分记录数: \(report.pointRecordAnalysis.totalRecords)")
    }
    
    /**
     * 测试诊断报告文本生成
     */
    func testDiagnosticReportGeneration() async {
        let diagnosticTool = await MainActor.run { DataDiagnosticTool.shared }
        let report = await diagnosticTool.performFullDiagnostic()
        let reportText = await MainActor.run { diagnosticTool.generateReportText(report) }

        // 验证报告文本包含关键信息
        XCTAssertTrue(reportText.contains("数据诊断报告"), "报告应该包含标题")
        XCTAssertTrue(reportText.contains("概览"), "报告应该包含概览部分")
        XCTAssertTrue(reportText.contains("用户数据分析"), "报告应该包含用户数据分析")
        XCTAssertTrue(reportText.contains("成员数据分析"), "报告应该包含成员数据分析")
        XCTAssertTrue(reportText.contains("积分记录分析"), "报告应该包含积分记录分析")

        print("生成的报告文本长度: \(reportText.count) 字符")
    }
    
    // MARK: - 数据完整性测试
    
    /**
     * 测试用户数据完整性检查
     */
    func testUserDataIntegrityCheck() async {
        await MainActor.run {
            let coreDataManager = CoreDataManager.shared
            let issues = coreDataManager.checkUserDataIntegrity()

            // 验证完整性检查能够正常运行
            XCTAssertTrue(issues.count >= 0, "用户数据完整性检查应该能够正常运行")

            print("用户数据完整性检查发现 \(issues.count) 个问题")
            for issue in issues {
                print("- [\(issue.severity)] \(issue.description)")
            }
        }
    }
    
    /**
     * 测试成员数据完整性检查
     */
    func testMemberDataIntegrityCheck() async {
        await MainActor.run {
            let coreDataManager = CoreDataManager.shared
            let issues = coreDataManager.checkMemberDataIntegrity()

            // 验证完整性检查能够正常运行
            XCTAssertTrue(issues.count >= 0, "成员数据完整性检查应该能够正常运行")

            print("成员数据完整性检查发现 \(issues.count) 个问题")
            for issue in issues {
                print("- [\(issue.severity)] \(issue.description)")
            }
        }
    }
    
    /**
     * 测试积分记录完整性检查
     */
    func testPointRecordIntegrityCheck() async {
        await MainActor.run {
            let coreDataManager = CoreDataManager.shared
            let issues = coreDataManager.checkPointRecordIntegrity()

            // 验证完整性检查能够正常运行
            XCTAssertTrue(issues.count >= 0, "积分记录完整性检查应该能够正常运行")

            print("积分记录完整性检查发现 \(issues.count) 个问题")
            for issue in issues {
                print("- [\(issue.severity)] \(issue.description)")
            }
        }
    }
    
    // MARK: - 数据恢复测试
    
    /**
     * 测试数据恢复功能
     */
    func testDataRecovery() async {
        let multiLayerStorage = await MainActor.run { MultiLayerStorageManager.shared }

        // 执行数据恢复
        await multiLayerStorage.performDataRecovery()

        // 验证恢复状态
        await MainActor.run {
            switch multiLayerStorage.recoveryStatus {
            case .completed(let recoveredItems):
                print("数据恢复完成，恢复了 \(recoveredItems) 项数据")
                XCTAssertTrue(recoveredItems >= 0, "恢复的数据项数应该大于等于0")

            case .failed(let error):
                print("数据恢复失败: \(error)")
                XCTFail("数据恢复不应该失败")

            default:
                print("数据恢复状态: \(multiLayerStorage.recoveryStatus)")
            }
        }
    }
    
    // MARK: - 性能测试
    
    /**
     * 测试多层存储性能
     */
    func testMultiLayerStoragePerformance() async {
        let testKey = "performance_test_key"
        let testValue = "performance_test_value"

        measure {
            let expectation = XCTestExpectation(description: "性能测试完成")
            Task {
                await MainActor.run {
                    let multiLayerStorage = MultiLayerStorageManager.shared

                    // 测试存储性能
                    for i in 0..<100 {
                        multiLayerStorage.store("\(testValue)_\(i)", forKey: "\(testKey)_\(i)")
                    }

                    // 测试检索性能
                    for i in 0..<100 {
                        let _: String? = multiLayerStorage.retrieve(String.self, forKey: "\(testKey)_\(i)")
                    }
                }
                expectation.fulfill()
            }
            wait(for: [expectation], timeout: 10.0)
        }

        // 清理测试数据
        for i in 0..<100 {
            UserDefaults.standard.removeObject(forKey: "\(testKey)_\(i)")
            NSUbiquitousKeyValueStore.default.removeObject(forKey: "\(testKey)_\(i)")
        }
    }
    
    /**
     * 测试数据诊断性能
     */
    func testDataDiagnosticPerformance() {
        measure {
            let expectation = XCTestExpectation(description: "诊断完成")
            Task {
                let _ = await diagnosticTool.performFullDiagnostic()
                expectation.fulfill()
            }
            wait(for: [expectation], timeout: 10.0)
        }
    }
}
