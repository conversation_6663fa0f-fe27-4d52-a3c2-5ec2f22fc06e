//
//  MultiDeviceSyncSimulator.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import CoreData
import Combine

/**
 * 多设备同步模拟器
 * 
 * 模拟多个设备之间的数据同步场景，验证：
 * - 数据一致性
 * - 冲突解决
 * - 网络异常处理
 * - 离线操作同步
 */
class MultiDeviceSyncSimulator {
    
    // MARK: - Properties
    private let dataManager: DataManager
    private let persistenceController: PersistenceController
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Test Results
    struct TestResult {
        let testName: String
        let success: Bool
        let details: String
        let duration: TimeInterval
    }
    
    struct SyncTestReport {
        let timestamp: Date
        let totalTests: Int
        let passedTests: Int
        let failedTests: Int
        let testResults: [TestResult]
        
        var successRate: Double {
            return totalTests > 0 ? Double(passedTests) / Double(totalTests) : 0.0
        }
    }
    
    // MARK: - Initialization
    init() {
        self.dataManager = DataManager.shared
        self.persistenceController = PersistenceController.shared
    }
    
    // MARK: - 同步场景测试
    
    /**
     * 执行完整的多设备同步测试
     */
    func runFullSyncTest() -> SyncTestReport {
        print("🧪 开始多设备同步测试...")
        
        var testResults: [TestResult] = []
        
        // 测试1: 基本数据同步
        testResults.append(testBasicDataSync())
        
        // 测试2: 并发操作处理
        testResults.append(testConcurrentOperations())
        
        // 测试3: 网络中断恢复
        testResults.append(testNetworkInterruption())
        
        // 测试4: 数据冲突解决
        testResults.append(testConflictResolution())
        
        // 测试5: 大量数据同步
        testResults.append(testBulkDataSync())
        
        let passedTests = testResults.filter { $0.success }.count
        let report = SyncTestReport(
            timestamp: Date(),
            totalTests: testResults.count,
            passedTests: passedTests,
            failedTests: testResults.count - passedTests,
            testResults: testResults
        )
        
        print("✅ 多设备同步测试完成，成功率: \(String(format: "%.1f", report.successRate * 100))%")
        
        return report
    }
    
    // MARK: - 具体测试方法
    
    /**
     * 测试基本数据同步
     */
    private func testBasicDataSync() -> TestResult {
        let startTime = Date()
        print("🔄 测试基本数据同步...")

        // 创建测试成员
        guard let testMember = createTestMember(name: "同步测试成员") else {
            return TestResult(
                testName: "基本数据同步",
                success: false,
                details: "创建测试成员失败",
                duration: Date().timeIntervalSince(startTime)
            )
        }

        // 添加积分记录
        Task { @MainActor in
            dataManager.addPointRecord(to: testMember, reason: "同步测试", value: 10)
        }

        // 等待同步
        Thread.sleep(forTimeInterval: 2.0)

        // 验证数据是否正确保存
        let savedRecords = testMember.pointRecords?.allObjects as? [PointRecord] ?? []
        let success = savedRecords.contains { $0.reason == "同步测试" && $0.value == 10 }

        // 清理测试数据
        cleanupTestMember(testMember)

        return TestResult(
            testName: "基本数据同步",
            success: success,
            details: success ? "数据同步成功" : "数据同步失败",
            duration: Date().timeIntervalSince(startTime)
        )
    }
    
    /**
     * 测试并发操作处理
     */
    private func testConcurrentOperations() -> TestResult {
        let startTime = Date()
        print("⚡ 测试并发操作处理...")

        guard let testMember = createTestMember(name: "并发测试成员") else {
            return TestResult(
                testName: "并发操作处理",
                success: false,
                details: "创建测试成员失败",
                duration: Date().timeIntervalSince(startTime)
            )
        }

        let initialPoints = testMember.currentPoints

        // 模拟多个设备同时操作
        let group = DispatchGroup()

        for i in 1...5 {
            group.enter()
            Task { @MainActor in
                self.dataManager.addPointRecord(to: testMember, reason: "并发测试\(i)", value: 1)
                group.leave()
            }
        }

        group.wait()

        // 等待同步完成
        Thread.sleep(forTimeInterval: 3.0)

        // 验证最终积分是否正确
        let finalPoints = testMember.currentPoints
        let expectedPoints = initialPoints + 5
        let success = finalPoints == expectedPoints

        // 清理测试数据
        cleanupTestMember(testMember)

        return TestResult(
            testName: "并发操作处理",
            success: success,
            details: success ? "并发操作处理成功" : "积分计算错误: 期望\(expectedPoints)，实际\(finalPoints)",
            duration: Date().timeIntervalSince(startTime)
        )
    }
    
    /**
     * 测试网络中断恢复
     */
    private func testNetworkInterruption() -> TestResult {
        let startTime = Date()
        print("📡 测试网络中断恢复...")
        
        // 这是一个模拟测试，实际网络中断难以在单元测试中实现
        // 我们模拟离线操作然后同步的场景
        
        do {
            guard let testMember = createTestMember(name: "网络测试成员") else {
                return TestResult(
                    testName: "网络中断恢复",
                    success: false,
                    details: "创建测试成员失败",
                    duration: Date().timeIntervalSince(startTime)
                )
            }
            
            // 模拟离线操作
            Task { @MainActor in
                dataManager.addPointRecord(to: testMember, reason: "离线操作1", value: 5)
                dataManager.addPointRecord(to: testMember, reason: "离线操作2", value: 3)
            }
            
            // 模拟网络恢复，触发同步
            persistenceController.forceCloudKitSync()
            
            // 等待同步完成
            Thread.sleep(forTimeInterval: 3.0)
            
            // 验证数据是否正确同步
            let records = testMember.pointRecords?.allObjects as? [PointRecord] ?? []
            let offlineRecords = records.filter { $0.reason?.contains("离线操作") == true }
            let success = offlineRecords.count == 2
            
            // 清理测试数据
            cleanupTestMember(testMember)
            
            return TestResult(
                testName: "网络中断恢复",
                success: success,
                details: success ? "网络恢复同步成功" : "离线数据同步失败",
                duration: Date().timeIntervalSince(startTime)
            )
            
        } catch {
            return TestResult(
                testName: "网络中断恢复",
                success: false,
                details: "测试异常: \(error.localizedDescription)",
                duration: Date().timeIntervalSince(startTime)
            )
        }
    }
    
    /**
     * 测试数据冲突解决
     */
    private func testConflictResolution() -> TestResult {
        let startTime = Date()
        print("⚔️ 测试数据冲突解决...")
        
        // 模拟两个设备修改同一数据的冲突场景
        // 在实际应用中，CloudKit会自动处理大部分冲突
        
        do {
            guard let testMember = createTestMember(name: "冲突测试成员") else {
                return TestResult(
                    testName: "数据冲突解决",
                    success: false,
                    details: "创建测试成员失败",
                    duration: Date().timeIntervalSince(startTime)
                )
            }
            
            // 模拟设备A的操作
            Task { @MainActor in
                dataManager.addPointRecord(to: testMember, reason: "设备A操作", value: 10)

                // 模拟设备B的操作（几乎同时）
                dataManager.addPointRecord(to: testMember, reason: "设备B操作", value: 5)
            }
            
            // 等待冲突解决
            Thread.sleep(forTimeInterval: 3.0)
            
            // 验证两个操作都成功保存
            let records = testMember.pointRecords?.allObjects as? [PointRecord] ?? []
            let deviceARecord = records.first { $0.reason == "设备A操作" }
            let deviceBRecord = records.first { $0.reason == "设备B操作" }
            let success = deviceARecord != nil && deviceBRecord != nil
            
            // 清理测试数据
            cleanupTestMember(testMember)
            
            return TestResult(
                testName: "数据冲突解决",
                success: success,
                details: success ? "冲突解决成功" : "冲突解决失败",
                duration: Date().timeIntervalSince(startTime)
            )
            
        } catch {
            return TestResult(
                testName: "数据冲突解决",
                success: false,
                details: "测试异常: \(error.localizedDescription)",
                duration: Date().timeIntervalSince(startTime)
            )
        }
    }
    
    /**
     * 测试大量数据同步
     */
    private func testBulkDataSync() -> TestResult {
        let startTime = Date()
        print("📊 测试大量数据同步...")
        
        do {
            guard let testMember = createTestMember(name: "批量测试成员") else {
                return TestResult(
                    testName: "大量数据同步",
                    success: false,
                    details: "创建测试成员失败",
                    duration: Date().timeIntervalSince(startTime)
                )
            }
            
            let recordCount = 20
            
            // 批量添加积分记录
            Task { @MainActor in
                for i in 1...recordCount {
                    dataManager.addPointRecord(to: testMember, reason: "批量记录\(i)", value: Int32(i))
                }
            }
            
            // 等待同步完成
            Thread.sleep(forTimeInterval: 5.0)
            
            // 验证所有记录都已保存
            let records = testMember.pointRecords?.allObjects as? [PointRecord] ?? []
            let batchRecords = records.filter { $0.reason?.contains("批量记录") == true }
            let success = batchRecords.count == recordCount
            
            // 清理测试数据
            cleanupTestMember(testMember)
            
            return TestResult(
                testName: "大量数据同步",
                success: success,
                details: success ? "批量数据同步成功" : "批量数据同步失败，期望\(recordCount)条，实际\(batchRecords.count)条",
                duration: Date().timeIntervalSince(startTime)
            )
            
        } catch {
            return TestResult(
                testName: "大量数据同步",
                success: false,
                details: "测试异常: \(error.localizedDescription)",
                duration: Date().timeIntervalSince(startTime)
            )
        }
    }

    // MARK: - 辅助方法

    /**
     * 创建测试成员
     */
    private func createTestMember(name: String) -> Member? {
        var createdMember: Member?

        Task { @MainActor in
            guard let currentUser = dataManager.currentUser else {
                print("❌ 无法获取当前用户")
                return
            }

            let member = Member(context: dataManager.viewContext)
            member.id = UUID()
            member.name = name
            member.role = "test"
            member.currentPoints = 0
            member.createdAt = Date()
            member.updatedAt = Date()
            member.user = currentUser

            dataManager.save()
            createdMember = member
        }

        // 等待创建完成
        Thread.sleep(forTimeInterval: 0.5)
        return createdMember
    }

    /**
     * 清理测试成员
     */
    private func cleanupTestMember(_ member: Member) {
        Task { @MainActor in
            // 删除相关的积分记录
            if let pointRecords = member.pointRecords?.allObjects as? [PointRecord] {
                for record in pointRecords {
                    dataManager.viewContext.delete(record)
                }
            }

            // 删除成员
            dataManager.viewContext.delete(member)
            dataManager.save()

            print("🧹 已清理测试成员: \(member.name ?? "未知")")
        }
    }

    /**
     * 生成测试报告文本
     */
    func generateReportText(_ report: SyncTestReport) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .medium

        var text = """
        📱 多设备同步测试报告
        生成时间: \(formatter.string(from: report.timestamp))

        📊 测试统计:
        • 总测试数: \(report.totalTests)
        • 通过测试: \(report.passedTests)
        • 失败测试: \(report.failedTests)
        • 成功率: \(String(format: "%.1f", report.successRate * 100))%

        📋 详细结果:
        """

        for (index, result) in report.testResults.enumerated() {
            let status = result.success ? "✅" : "❌"
            let duration = String(format: "%.2f", result.duration)
            text += "\n\(index + 1). \(status) \(result.testName) (\(duration)s)"
            if !result.success {
                text += "\n   错误: \(result.details)"
            }
        }

        if report.failedTests > 0 {
            text += "\n\n⚠️ 建议:\n"
            text += "• 检查网络连接状态\n"
            text += "• 验证iCloud账户登录状态\n"
            text += "• 运行数据一致性检查\n"
            text += "• 重启应用后重新测试"
        } else {
            text += "\n\n🎉 所有测试通过！数据同步功能正常。"
        }

        return text
    }

    /**
     * 快速同步测试
     */
    func runQuickSyncTest() -> Bool {
        print("⚡ 执行快速同步测试...")

        let result = testBasicDataSync()
        print("快速测试结果: \(result.success ? "通过" : "失败")")

        return result.success
    }

    /**
     * 压力测试
     */
    func runStressTest(duration: TimeInterval = 60.0) -> SyncTestReport {
        print("💪 开始压力测试，持续时间: \(duration) 秒...")

        let startTime = Date()
        var testResults: [TestResult] = []
        var testCount = 0

        while Date().timeIntervalSince(startTime) < duration {
            testCount += 1
            print("执行压力测试 #\(testCount)...")

            let result = testBasicDataSync()
            testResults.append(TestResult(
                testName: "压力测试 #\(testCount)",
                success: result.success,
                details: result.details,
                duration: result.duration
            ))

            // 短暂休息
            Thread.sleep(forTimeInterval: 1.0)
        }

        let passedTests = testResults.filter { $0.success }.count
        let report = SyncTestReport(
            timestamp: Date(),
            totalTests: testResults.count,
            passedTests: passedTests,
            failedTests: testResults.count - passedTests,
            testResults: testResults
        )

        print("✅ 压力测试完成，执行了 \(testCount) 次测试")

        return report
    }
}
