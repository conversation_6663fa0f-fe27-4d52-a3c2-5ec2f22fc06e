//
//  DataDiagnosticTool.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit

/**
 * 数据诊断工具
 * 
 * 提供完整的数据诊断功能，包括：
 * - 数据完整性检查
 * - CloudKit同步状态分析
 * - 数据一致性验证
 * - 自动修复建议
 */
class DataDiagnosticTool {
    
    // MARK: - Shared Instance
    static let shared = DataDiagnosticTool()
    
    // MARK: - Private Properties
    private var coreDataManager: CoreDataManager {
        CoreDataManager.shared
    }
    private var dataManager: DataManager {
        DataManager.shared
    }

    private init() {}
    
    // MARK: - 诊断报告结构
    
    struct DiagnosticReport {
        let timestamp: Date
        let userAnalysis: UserAnalysis
        let memberAnalysis: MemberAnalysis
        let pointRecordAnalysis: PointRecordAnalysis
        let cloudKitStatus: CloudKitStatus
        let consistencyIssues: [ConsistencyIssue]
        let recommendations: [String]
        
        var summary: String {
            let issueCount = consistencyIssues.count
            let status = issueCount == 0 ? "健康" : "发现 \(issueCount) 个问题"
            return "数据状态: \(status)"
        }
    }
    
    struct UserAnalysis {
        let totalUsers: Int
        let usersWithSubscription: Int
        let usersWithoutSubscription: Int
        let duplicateUsers: Int
    }
    
    struct MemberAnalysis {
        let totalMembers: Int
        let membersWithUser: Int
        let orphanedMembers: Int
        let duplicateMembers: Int
        let membersWithNegativePoints: Int
    }
    
    struct PointRecordAnalysis {
        let totalRecords: Int
        let recordsWithMember: Int
        let orphanedRecords: Int
        let recordsWithoutTimestamp: Int
        let recordsWithoutReason: Int
        let duplicateRecords: Int
    }
    
    struct CloudKitStatus {
        let isEnabled: Bool
        let lastSyncDate: Date?
        let syncStatus: String
        let accountStatus: String
    }
    
    struct ConsistencyIssue {
        let type: IssueType
        let description: String
        let severity: Severity
        let affectedCount: Int
        
        enum IssueType {
            case duplicateUsers
            case orphanedMembers
            case orphanedPointRecords
            case missingSubscription
            case inconsistentUserData
            case cloudKitSyncIssue
            case negativePoints
            case missingTimestamp
            case missingReason
        }
        
        enum Severity {
            case low
            case medium
            case high
            case critical
            
            var displayText: String {
                switch self {
                case .low: return "低"
                case .medium: return "中"
                case .high: return "高"
                case .critical: return "严重"
                }
            }
        }
    }
    
    // MARK: - 公共方法
    
    /**
     * 执行完整的数据诊断
     */
    func performFullDiagnostic() -> DiagnosticReport {
        print("🔍 开始完整数据诊断...")
        
        let userAnalysis = analyzeUsers()
        let memberAnalysis = analyzeMembers()
        let pointRecordAnalysis = analyzePointRecords()
        let cloudKitStatus = analyzeCloudKitStatus()
        let consistencyIssues = detectConsistencyIssues()
        let recommendations = generateRecommendations(issues: consistencyIssues)
        
        let report = DiagnosticReport(
            timestamp: Date(),
            userAnalysis: userAnalysis,
            memberAnalysis: memberAnalysis,
            pointRecordAnalysis: pointRecordAnalysis,
            cloudKitStatus: cloudKitStatus,
            consistencyIssues: consistencyIssues,
            recommendations: recommendations
        )
        
        print("✅ 数据诊断完成，发现 \(consistencyIssues.count) 个问题")
        
        return report
    }
    
    /**
     * 快速健康检查
     */
    func performQuickHealthCheck() -> Bool {
        let issues = detectConsistencyIssues()
        let criticalIssues = issues.filter { $0.severity == .critical || $0.severity == .high }
        return criticalIssues.isEmpty
    }
    
    /**
     * 自动修复数据问题
     */
    func autoFixIssues() -> Bool {
        print("🔧 开始自动修复数据问题...")

        Task { @MainActor in
            coreDataManager.triggerDataConsistencyCheck()
        }
        print("✅ 自动修复完成")
        return true
    }
    
    /**
     * 生成诊断报告文本
     */
    func generateReportText(_ report: DiagnosticReport) -> String {
        var text = """
        📊 数据诊断报告
        生成时间: \(DateFormatter.fullDateTime.string(from: report.timestamp))
        
        📈 数据统计:
        • 用户总数: \(report.userAnalysis.totalUsers)
        • 成员总数: \(report.memberAnalysis.totalMembers)
        • 积分记录总数: \(report.pointRecordAnalysis.totalRecords)
        
        ☁️ CloudKit状态:
        • 同步状态: \(report.cloudKitStatus.syncStatus)
        • 账户状态: \(report.cloudKitStatus.accountStatus)
        • 最后同步: \(report.cloudKitStatus.lastSyncDate?.description ?? "未知")
        
        """
        
        if !report.consistencyIssues.isEmpty {
            text += "\n⚠️ 发现的问题:\n"
            for (index, issue) in report.consistencyIssues.enumerated() {
                text += "\(index + 1). [\(issue.severity.displayText)] \(issue.description)\n"
            }
        }
        
        if !report.recommendations.isEmpty {
            text += "\n💡 建议:\n"
            for (index, recommendation) in report.recommendations.enumerated() {
                text += "\(index + 1). \(recommendation)\n"
            }
        }
        
        return text
    }
    
    // MARK: - 私有分析方法
    
    private func analyzeUsers() -> UserAnalysis {
        let request: NSFetchRequest<User> = User.fetchRequest()
        var result = UserAnalysis(totalUsers: 0, usersWithSubscription: 0, usersWithoutSubscription: 0, duplicateUsers: 0)

        Task { @MainActor in
            do {
                let users = try coreDataManager.viewContext.fetch(request)
                let usersWithSubscription = users.filter { $0.subscription != nil }.count
                let duplicates = findDuplicateUsers(users)

                result = UserAnalysis(
                    totalUsers: users.count,
                    usersWithSubscription: usersWithSubscription,
                    usersWithoutSubscription: users.count - usersWithSubscription,
                    duplicateUsers: duplicates
                )
            } catch {
                print("❌ 分析用户数据失败: \(error)")
            }
        }

        return result
    }
    
    private func analyzeMembers() -> MemberAnalysis {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        var result = MemberAnalysis(totalMembers: 0, membersWithUser: 0, orphanedMembers: 0, duplicateMembers: 0, membersWithNegativePoints: 0)

        Task { @MainActor in
            do {
                let members = try coreDataManager.viewContext.fetch(request)
                let membersWithUser = members.filter { $0.user != nil }.count
                let membersWithNegativePoints = members.filter { $0.currentPoints < 0 }.count
                let duplicates = findDuplicateMembers(members)

                result = MemberAnalysis(
                    totalMembers: members.count,
                    membersWithUser: membersWithUser,
                    orphanedMembers: members.count - membersWithUser,
                    duplicateMembers: duplicates,
                    membersWithNegativePoints: membersWithNegativePoints
                )
            } catch {
                print("❌ 分析成员数据失败: \(error)")
            }
        }

        return result
    }
    
    private func analyzePointRecords() -> PointRecordAnalysis {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        var result = PointRecordAnalysis(totalRecords: 0, recordsWithMember: 0, orphanedRecords: 0, recordsWithoutTimestamp: 0, recordsWithoutReason: 0, duplicateRecords: 0)

        Task { @MainActor in
            do {
                let records = try coreDataManager.viewContext.fetch(request)
                let recordsWithMember = records.filter { $0.member != nil }.count
                let recordsWithoutTimestamp = records.filter { $0.timestamp == nil }.count
                let recordsWithoutReason = records.filter { $0.reason?.isEmpty == true }.count
                let duplicates = findDuplicatePointRecords(records)

                result = PointRecordAnalysis(
                    totalRecords: records.count,
                    recordsWithMember: recordsWithMember,
                    orphanedRecords: records.count - recordsWithMember,
                    recordsWithoutTimestamp: recordsWithoutTimestamp,
                    recordsWithoutReason: recordsWithoutReason,
                    duplicateRecords: duplicates
                )
            } catch {
                print("❌ 分析积分记录失败: \(error)")
            }
        }

        return result
    }
    
    private func analyzeCloudKitStatus() -> CloudKitStatus {
        return CloudKitStatus(
            isEnabled: true, // ztt2统一启用CloudKit
            lastSyncDate: coreDataManager.lastSyncDate,
            syncStatus: "正常", // 简化处理
            accountStatus: "已登录" // 简化处理
        )
    }

    // MARK: - 重复数据检测

    private func findDuplicateUsers(_ users: [User]) -> Int {
        var seenAppleUserIDs = Set<String>()
        var duplicateCount = 0

        for user in users {
            if let appleUserID = user.appleUserID {
                if seenAppleUserIDs.contains(appleUserID) {
                    duplicateCount += 1
                } else {
                    seenAppleUserIDs.insert(appleUserID)
                }
            }
        }

        return duplicateCount
    }

    private func findDuplicateMembers(_ members: [Member]) -> Int {
        var seenMemberKeys = Set<String>()
        var duplicateCount = 0

        for member in members {
            let memberKey = "\(member.user?.id?.uuidString ?? "")_\(member.name ?? "")_\(member.role ?? "")"

            if seenMemberKeys.contains(memberKey) {
                duplicateCount += 1
            } else {
                seenMemberKeys.insert(memberKey)
            }
        }

        return duplicateCount
    }

    private func findDuplicatePointRecords(_ records: [PointRecord]) -> Int {
        var seenRecordKeys = Set<String>()
        var duplicateCount = 0

        for record in records {
            let recordKey = "\(record.member?.id?.uuidString ?? "")_\(record.reason ?? "")_\(record.value)_\(record.timestamp?.timeIntervalSince1970 ?? 0)"

            if seenRecordKeys.contains(recordKey) {
                duplicateCount += 1
            } else {
                seenRecordKeys.insert(recordKey)
            }
        }

        return duplicateCount
    }

    // MARK: - 一致性问题检测

    private func detectConsistencyIssues() -> [ConsistencyIssue] {
        var issues: [ConsistencyIssue] = []

        // 检查重复用户
        let userAnalysis = analyzeUsers()
        if userAnalysis.duplicateUsers > 0 {
            issues.append(ConsistencyIssue(
                type: .duplicateUsers,
                description: "发现 \(userAnalysis.duplicateUsers) 个重复用户",
                severity: .high,
                affectedCount: userAnalysis.duplicateUsers
            ))
        }

        // 检查孤立成员
        let memberAnalysis = analyzeMembers()
        if memberAnalysis.orphanedMembers > 0 {
            issues.append(ConsistencyIssue(
                type: .orphanedMembers,
                description: "发现 \(memberAnalysis.orphanedMembers) 个孤立成员",
                severity: .medium,
                affectedCount: memberAnalysis.orphanedMembers
            ))
        }

        // 检查负积分
        if memberAnalysis.membersWithNegativePoints > 0 {
            issues.append(ConsistencyIssue(
                type: .negativePoints,
                description: "发现 \(memberAnalysis.membersWithNegativePoints) 个成员积分为负数",
                severity: .medium,
                affectedCount: memberAnalysis.membersWithNegativePoints
            ))
        }

        // 检查孤立积分记录
        let pointRecordAnalysis = analyzePointRecords()
        if pointRecordAnalysis.orphanedRecords > 0 {
            issues.append(ConsistencyIssue(
                type: .orphanedPointRecords,
                description: "发现 \(pointRecordAnalysis.orphanedRecords) 个孤立积分记录",
                severity: .high,
                affectedCount: pointRecordAnalysis.orphanedRecords
            ))
        }

        // 检查缺少订阅的用户
        if userAnalysis.usersWithoutSubscription > 0 {
            issues.append(ConsistencyIssue(
                type: .missingSubscription,
                description: "发现 \(userAnalysis.usersWithoutSubscription) 个用户缺少订阅信息",
                severity: .medium,
                affectedCount: userAnalysis.usersWithoutSubscription
            ))
        }

        return issues
    }

    // MARK: - 建议生成

    private func generateRecommendations(issues: [ConsistencyIssue]) -> [String] {
        var recommendations: [String] = []

        if issues.isEmpty {
            recommendations.append("✅ 数据状态良好，无需特殊处理")
            return recommendations
        }

        let duplicateDataIssues = issues.filter { $0.type == .duplicateUsers }
        if !duplicateDataIssues.isEmpty {
            recommendations.append("🔄 建议运行数据一致性检查以清理重复数据")
        }

        let orphanedDataIssues = issues.filter { $0.type == .orphanedMembers || $0.type == .orphanedPointRecords }
        if !orphanedDataIssues.isEmpty {
            recommendations.append("🏠 建议将孤立数据关联到当前用户或删除")
        }

        let missingSubscriptionIssues = issues.filter { $0.type == .missingSubscription }
        if !missingSubscriptionIssues.isEmpty {
            recommendations.append("📋 建议为缺少订阅信息的用户创建默认订阅")
        }

        let negativePointsIssues = issues.filter { $0.type == .negativePoints }
        if !negativePointsIssues.isEmpty {
            recommendations.append("🔢 建议将负积分重置为0")
        }

        recommendations.append("🔧 可以使用自动修复功能处理大部分问题")
        recommendations.append("☁️ 建议定期检查CloudKit同步状态")

        return recommendations
    }
}


