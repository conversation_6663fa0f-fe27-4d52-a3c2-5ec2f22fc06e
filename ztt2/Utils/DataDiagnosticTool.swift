//
//  DataDiagnosticTool.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import CoreData
import Combine

/**
 * 数据诊断工具
 * 用于检测和修复数据问题，提供详细的诊断报告
 */
@MainActor
class DataDiagnosticTool: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataDiagnosticTool()
    
    // MARK: - Published Properties
    @Published var isRunning: Bool = false
    @Published var lastDiagnosticDate: Date?
    @Published var lastReport: DiagnosticReport?
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let multiLayerStorage = MultiLayerStorageManager.shared
    private let dataManager = DataManager.shared
    
    // MARK: - Types
    struct DiagnosticReport {
        let timestamp: Date
        let userAnalysis: UserAnalysis
        let memberAnalysis: MemberAnalysis
        let pointRecordAnalysis: PointRecordAnalysis
        let storageConsistencyAnalysis: StorageConsistencyAnalysis
        let issuesFound: [DataIntegrityIssue]
        let issuesFixed: [DataIntegrityIssue]
        let recommendations: [String]
        
        var totalIssues: Int { issuesFound.count }
        var criticalIssues: Int { issuesFound.filter { $0.severity == .critical }.count }
        var fixedIssues: Int { issuesFixed.count }
        var remainingIssues: Int { totalIssues - fixedIssues }
    }
    
    struct UserAnalysis {
        let totalUsers: Int
        let usersWithSubscription: Int
        let usersWithoutMembers: Int
        let duplicateUsers: Int
    }
    
    struct MemberAnalysis {
        let totalMembers: Int
        let orphanedMembers: Int
        let membersWithNegativePoints: Int
        let membersWithoutNames: Int
    }
    
    struct PointRecordAnalysis {
        let totalRecords: Int
        let orphanedRecords: Int
        let recordsWithoutTimestamp: Int
        let duplicateRecords: Int
    }
    
    struct StorageConsistencyAnalysis {
        let coreDataStatus: String
        let ubiquitousStoreStatus: String
        let userDefaultsStatus: String
        let inconsistencies: [String]
    }
    
    // MARK: - Initialization
    private init() {
        loadLastDiagnosticDate()
    }
    
    // MARK: - Public Methods
    
    /**
     * 执行完整的数据诊断
     */
    func performFullDiagnostic() async -> DiagnosticReport {
        await MainActor.run {
            isRunning = true
        }
        
        print("🔍 开始执行完整数据诊断...")
        
        let userAnalysis = await analyzeUsers()
        let memberAnalysis = await analyzeMembers()
        let pointRecordAnalysis = await analyzePointRecords()
        let storageConsistencyAnalysis = await analyzeStorageConsistency()
        
        // 收集所有问题
        var allIssues: [DataIntegrityIssue] = []
        allIssues.append(contentsOf: coreDataManager.checkUserDataIntegrity())
        allIssues.append(contentsOf: coreDataManager.checkMemberDataIntegrity())
        allIssues.append(contentsOf: coreDataManager.checkPointRecordIntegrity())
        
        // 生成建议
        let recommendations = generateRecommendations(for: allIssues)
        
        let report = DiagnosticReport(
            timestamp: Date(),
            userAnalysis: userAnalysis,
            memberAnalysis: memberAnalysis,
            pointRecordAnalysis: pointRecordAnalysis,
            storageConsistencyAnalysis: storageConsistencyAnalysis,
            issuesFound: allIssues,
            issuesFixed: [], // 诊断阶段不修复问题
            recommendations: recommendations
        )
        
        await MainActor.run {
            self.lastReport = report
            self.lastDiagnosticDate = Date()
            self.isRunning = false
            self.saveLastDiagnosticDate()
        }
        
        print("✅ 数据诊断完成 - 发现 \(allIssues.count) 个问题")
        
        return report
    }
    
    /**
     * 执行数据修复
     */
    func performDataRepair() async -> DiagnosticReport {
        await MainActor.run {
            isRunning = true
        }
        
        print("🔧 开始执行数据修复...")
        
        // 先执行诊断
        let diagnosticReport = await performFullDiagnostic()
        
        // 执行修复
        let fixedIssues = await repairDataIssues(diagnosticReport.issuesFound)
        
        // 重新诊断以验证修复结果
        let postRepairAnalysis = await performFullDiagnostic()
        
        let repairReport = DiagnosticReport(
            timestamp: Date(),
            userAnalysis: postRepairAnalysis.userAnalysis,
            memberAnalysis: postRepairAnalysis.memberAnalysis,
            pointRecordAnalysis: postRepairAnalysis.pointRecordAnalysis,
            storageConsistencyAnalysis: postRepairAnalysis.storageConsistencyAnalysis,
            issuesFound: diagnosticReport.issuesFound,
            issuesFixed: fixedIssues,
            recommendations: postRepairAnalysis.recommendations
        )
        
        await MainActor.run {
            self.lastReport = repairReport
            self.isRunning = false
        }
        
        print("✅ 数据修复完成 - 修复了 \(fixedIssues.count) 个问题")
        
        return repairReport
    }
    
    /**
     * 生成诊断报告文本
     */
    func generateReportText(_ report: DiagnosticReport) -> String {
        var text = "# 数据诊断报告\n\n"
        text += "**诊断时间**: \(DateFormatter.fullDateTime.string(from: report.timestamp))\n\n"
        
        // 概览
        text += "## 概览\n"
        text += "- 总问题数: \(report.totalIssues)\n"
        text += "- 严重问题: \(report.criticalIssues)\n"
        text += "- 已修复: \(report.fixedIssues)\n"
        text += "- 剩余问题: \(report.remainingIssues)\n\n"
        
        // 用户分析
        text += "## 用户数据分析\n"
        text += "- 总用户数: \(report.userAnalysis.totalUsers)\n"
        text += "- 有订阅信息: \(report.userAnalysis.usersWithSubscription)\n"
        text += "- 无成员用户: \(report.userAnalysis.usersWithoutMembers)\n"
        text += "- 重复用户: \(report.userAnalysis.duplicateUsers)\n\n"
        
        // 成员分析
        text += "## 成员数据分析\n"
        text += "- 总成员数: \(report.memberAnalysis.totalMembers)\n"
        text += "- 孤立成员: \(report.memberAnalysis.orphanedMembers)\n"
        text += "- 负积分成员: \(report.memberAnalysis.membersWithNegativePoints)\n"
        text += "- 无名称成员: \(report.memberAnalysis.membersWithoutNames)\n\n"
        
        // 积分记录分析
        text += "## 积分记录分析\n"
        text += "- 总记录数: \(report.pointRecordAnalysis.totalRecords)\n"
        text += "- 孤立记录: \(report.pointRecordAnalysis.orphanedRecords)\n"
        text += "- 无时间戳记录: \(report.pointRecordAnalysis.recordsWithoutTimestamp)\n"
        text += "- 重复记录: \(report.pointRecordAnalysis.duplicateRecords)\n\n"
        
        // 存储一致性分析
        text += "## 存储一致性分析\n"
        text += "- CoreData状态: \(report.storageConsistencyAnalysis.coreDataStatus)\n"
        text += "- iCloud状态: \(report.storageConsistencyAnalysis.ubiquitousStoreStatus)\n"
        text += "- 本地存储状态: \(report.storageConsistencyAnalysis.userDefaultsStatus)\n"
        
        if !report.storageConsistencyAnalysis.inconsistencies.isEmpty {
            text += "- 不一致问题:\n"
            for inconsistency in report.storageConsistencyAnalysis.inconsistencies {
                text += "  - \(inconsistency)\n"
            }
        }
        text += "\n"
        
        // 建议
        if !report.recommendations.isEmpty {
            text += "## 建议\n"
            for recommendation in report.recommendations {
                text += "- \(recommendation)\n"
            }
            text += "\n"
        }
        
        // 详细问题列表
        if !report.issuesFound.isEmpty {
            text += "## 详细问题列表\n"
            for (index, issue) in report.issuesFound.enumerated() {
                text += "\(index + 1). [\(issue.severity)] \(issue.description)\n"
            }
        }
        
        return text
    }
    
    // MARK: - Private Methods
    
    private func analyzeUsers() async -> UserAnalysis {
        let request: NSFetchRequest<User> = User.fetchRequest()
        
        do {
            let users = try coreDataManager.viewContext.fetch(request)
            
            let usersWithSubscription = users.filter { $0.subscription != nil }.count
            let usersWithoutMembers = users.filter { $0.members?.count == 0 }.count
            
            // 检查重复用户
            let usersByAppleID = Dictionary(grouping: users.compactMap { user in
                user.appleUserID != nil ? (user.appleUserID!, user) : nil
            }, by: { $0.0 })
            let duplicateUsers = usersByAppleID.values.filter { $0.count > 1 }.count
            
            return UserAnalysis(
                totalUsers: users.count,
                usersWithSubscription: usersWithSubscription,
                usersWithoutMembers: usersWithoutMembers,
                duplicateUsers: duplicateUsers
            )
        } catch {
            print("❌ 分析用户数据失败: \(error)")
            return UserAnalysis(totalUsers: 0, usersWithSubscription: 0, usersWithoutMembers: 0, duplicateUsers: 0)
        }
    }
    
    private func analyzeMembers() async -> MemberAnalysis {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        
        do {
            let members = try coreDataManager.viewContext.fetch(request)
            
            let orphanedMembers = members.filter { $0.user == nil }.count
            let membersWithNegativePoints = members.filter { $0.currentPoints < 0 }.count
            let membersWithoutNames = members.filter { $0.name?.isEmpty ?? true }.count
            
            return MemberAnalysis(
                totalMembers: members.count,
                orphanedMembers: orphanedMembers,
                membersWithNegativePoints: membersWithNegativePoints,
                membersWithoutNames: membersWithoutNames
            )
        } catch {
            print("❌ 分析成员数据失败: \(error)")
            return MemberAnalysis(totalMembers: 0, orphanedMembers: 0, membersWithNegativePoints: 0, membersWithoutNames: 0)
        }
    }
    
    private func analyzePointRecords() async -> PointRecordAnalysis {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        
        do {
            let records = try coreDataManager.viewContext.fetch(request)
            
            let orphanedRecords = records.filter { $0.member == nil }.count
            let recordsWithoutTimestamp = records.filter { $0.timestamp == nil }.count
            
            // 检查重复记录
            let groupedRecords = Dictionary(grouping: records) { record in
                "\(record.member?.id?.uuidString ?? "")-\(record.timestamp?.timeIntervalSince1970 ?? 0)-\(record.value)-\(record.reason ?? "")"
            }
            let duplicateRecords = groupedRecords.values.filter { $0.count > 1 }.count
            
            return PointRecordAnalysis(
                totalRecords: records.count,
                orphanedRecords: orphanedRecords,
                recordsWithoutTimestamp: recordsWithoutTimestamp,
                duplicateRecords: duplicateRecords
            )
        } catch {
            print("❌ 分析积分记录失败: \(error)")
            return PointRecordAnalysis(totalRecords: 0, orphanedRecords: 0, recordsWithoutTimestamp: 0, duplicateRecords: 0)
        }
    }
    
    private func analyzeStorageConsistency() async -> StorageConsistencyAnalysis {
        let coreDataStatus = coreDataManager.cloudKitSyncEnabled ? "正常" : "未启用"
        let ubiquitousStoreStatus = "正常" // 简化实现
        let userDefaultsStatus = "正常" // 简化实现
        
        let inconsistencies = multiLayerStorage.checkDataConsistency()
        
        return StorageConsistencyAnalysis(
            coreDataStatus: coreDataStatus,
            ubiquitousStoreStatus: ubiquitousStoreStatus,
            userDefaultsStatus: userDefaultsStatus,
            inconsistencies: inconsistencies
        )
    }
    
    private func generateRecommendations(for issues: [DataIntegrityIssue]) -> [String] {
        var recommendations: [String] = []
        
        let criticalIssues = issues.filter { $0.severity == .critical }
        if !criticalIssues.isEmpty {
            recommendations.append("立即修复 \(criticalIssues.count) 个严重问题")
        }
        
        let orphanedData = issues.filter { $0.type == .orphanedMember || $0.type == .orphanedPointRecord }
        if !orphanedData.isEmpty {
            recommendations.append("清理 \(orphanedData.count) 个孤立数据")
        }
        
        let missingData = issues.filter { $0.type == .missingSubscription || $0.type == .missingUserID }
        if !missingData.isEmpty {
            recommendations.append("补充 \(missingData.count) 个缺失数据")
        }
        
        if issues.contains(where: { $0.type == .duplicateUsers }) {
            recommendations.append("处理重复用户数据")
        }
        
        if recommendations.isEmpty {
            recommendations.append("数据状态良好，建议定期执行诊断")
        }
        
        return recommendations
    }
    
    private func repairDataIssues(_ issues: [DataIntegrityIssue]) async -> [DataIntegrityIssue] {
        // 这里实现具体的修复逻辑
        // 暂时返回空数组，表示没有修复任何问题
        return []
    }
    
    private func loadLastDiagnosticDate() {
        if let date = UserDefaults.standard.object(forKey: "last_diagnostic_date") as? Date {
            lastDiagnosticDate = date
        }
    }
    
    private func saveLastDiagnosticDate() {
        if let date = lastDiagnosticDate {
            UserDefaults.standard.set(date, forKey: "last_diagnostic_date")
        }
    }
}

// MARK: - DateFormatter Extension

extension DateFormatter {
    static let fullDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .medium
        return formatter
    }()
}
