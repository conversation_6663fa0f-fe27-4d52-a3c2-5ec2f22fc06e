//
//  AIReportDetailView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI报告详情页面
 * 参考ztt1项目ReportContentView设计，实现全屏沉浸式的报告展示体验
 */
struct AIReportDetailView: View {

    // MARK: - Properties

    let report: AIAnalysisReport
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    @State private var contentAppeared = false
    @State private var showingToast = false
    @State private var toastMessage = ""

    // MARK: - Body

    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 顶部占位，为导航栏留空间
                Color.clear
                    .frame(height: 80)

                VStack(spacing: 24) {
                    // 报告头部信息
                    reportHeaderSection

                    // 报告主要内容
                    reportMainContent

                    // 操作按钮
                    actionButtons

                    // 底部间距
                    Color.clear
                        .frame(height: 40)
                }
                .padding(.horizontal, 20)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8).delay(0.2)) {
                contentAppeared = true
            }
        }
        .overlay(
            // Toast提示
            VStack {
                Spacer()
                if showingToast {
                    ToastView(message: toastMessage)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showingToast)
                }
            }
            .padding(.bottom, 50)
        )
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    // MARK: - View Components





    /**
     * 报告头部信息
     */
    private var reportHeaderSection: some View {
        VStack(spacing: 20) {
            // 报告类型和标题
            reportTitleCard

            // 成员信息卡片
            memberInfoCard
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 20)
        .animation(.easeOut(duration: 0.6).delay(0.1), value: contentAppeared)
    }

    /**
     * 报告主要内容
     */
    private var reportMainContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 分析报告标题
            HStack {
                Image(systemName: reportTypeIcon)
                    .font(.system(size: 18))
                    .foregroundColor(Color(hex: "#74c07f"))

                Text("AI分析内容")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()
            }

            // AI分析内容
            analysisContentView
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: contentAppeared)
    }

    /**
     * 分析内容视图
     */
    private var analysisContentView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 使用优化的MarkdownText组件
            MarkdownText(content: report.content)
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.08), radius: 12, x: 0, y: 6)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#E8F5E8").opacity(0.8),
                            Color(hex: "#E8F5E8").opacity(0.3)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
    }

    /**
     * 报告标题卡片
     */
    private var reportTitleCard: some View {
        HStack(spacing: 16) {
            // 报告类型图标
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(hex: "#74c07f").opacity(0.2),
                                Color(hex: "#74c07f").opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 50, height: 50)

                Image(systemName: reportTypeIcon)
                    .font(.system(size: 22, weight: .medium))
                    .foregroundColor(Color(hex: "#74c07f"))
            }

            // 报告信息
            VStack(alignment: .leading, spacing: 6) {
                Text(report.reportType.displayName)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(reportDescription)
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }

            Spacer()
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
        )
    }

    /**
     * 成员信息卡片
     */
    private var memberInfoCard: some View {
        VStack(spacing: 16) {
            // 成员基本信息行
            HStack(spacing: 16) {
                // 成员头像占位符
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(hex: "#B5E36B").opacity(0.3),
                                    Color(hex: "#B5E36B").opacity(0.1)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 44, height: 44)

                    Text(String(report.memberName.prefix(1)))
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(Color(hex: "#74c07f"))
                }

                // 成员信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(report.memberName)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("\(report.memberRole) · \(report.memberAge)岁")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()

                // 生成时间标签
                VStack(alignment: .trailing, spacing: 4) {
                    Text("ai_report.detail.generated_time".localized)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textTertiary)

                    Text(DateFormatter.shortDateTime.string(from: report.createdAt))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }

            // 分隔线
            Rectangle()
                .fill(Color(hex: "#E8F5E8"))
                .frame(height: 1)

            // 报告统计信息
            HStack(spacing: 24) {
                // 报告类型标签
                HStack(spacing: 8) {
                    Image(systemName: "doc.text")
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "#74c07f"))

                    Text("ai_report.detail.report_type".localized)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text(report.reportType.displayName)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }

                Spacer()

                // 状态标签
                HStack(spacing: 6) {
                    Circle()
                        .fill(Color(hex: "#26C34B"))
                        .frame(width: 8, height: 8)

                    Text("ai_report.detail.status_completed".localized)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color(hex: "#26C34B"))
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.06), radius: 6, x: 0, y: 3)
        )
    }





    /**
     * 操作按钮区域
     */
    private var actionButtons: some View {
        VStack(spacing: 20) {
            // 操作按钮组
            HStack(spacing: 16) {
                // 复制按钮
                copyButton

                // 分享按钮
                shareButton
            }

            // 提示信息卡片
            infoTipsCard
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.5), value: contentAppeared)
    }

    /**
     * 提示信息卡片
     */
    private var infoTipsCard: some View {
        VStack(spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: "info.circle")
                    .font(.system(size: 14))
                    .foregroundColor(Color(hex: "#74c07f"))

                Text("ai_report.detail.usage_tips".localized)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 8) {
                HStack(alignment: .top, spacing: 8) {
                    Text("•")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("ai_report.detail.tip_text_selection".localized)
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()
                }

                HStack(alignment: .top, spacing: 8) {
                    Text("•")
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("ai_report.detail.tip_ai_reference".localized)
                        .font(.system(size: 12))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(hex: "#F8FDF0"))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(hex: "#E8F5E8"), lineWidth: 1)
                )
        )
    }

    /**
     * 复制按钮
     */
    private var copyButton: some View {
        Button(action: {
            copyReportToClipboard()
        }) {
            HStack(spacing: 10) {
                Image(systemName: "doc.on.doc")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)

                Text("ai_report.detail.copy_report".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#74c07f"),
                        Color(hex: "#5da961")
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(16)
            .shadow(color: Color(hex: "#74c07f").opacity(0.25), radius: 8, x: 0, y: 4)
        }
        .scaleEffect(contentAppeared ? 1.0 : 0.8)
        .animation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0), value: contentAppeared)
        .buttonStyle(PressedButtonStyle())
    }

    /**
     * 分享按钮
     */
    private var shareButton: some View {
        Button(action: {
            showingShareSheet = true
        }) {
            HStack(spacing: 10) {
                Image(systemName: "square.and.arrow.up")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(hex: "#74c07f"))

                Text("ai_report.detail.share_report".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(Color(hex: "#74c07f"))
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#74c07f").opacity(0.6),
                                        Color(hex: "#74c07f").opacity(0.3)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                    )
                    .shadow(color: Color.black.opacity(0.08), radius: 6, x: 0, y: 3)
            )
        }
        .scaleEffect(contentAppeared ? 1.0 : 0.8)
        .animation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0).delay(0.1), value: contentAppeared)
        .buttonStyle(PressedButtonStyle())
    }

    // MARK: - Computed Properties
    
    /// 报告类型图标
    private var reportTypeIcon: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "chart.line.uptrend.xyaxis"
        case .growthReport:
            return "heart.text.square"
        }
    }
    
    /// 报告描述
    private var reportDescription: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "基于积分记录数据，运用行为分析学理论生成的专业分析报告"
        case .growthReport:
            return "基于成长日记内容，运用儿童心理学理论生成的专业成长报告"
        }
    }
    
    // MARK: - Helper Methods

    /**
     * 复制报告到剪贴板
     */
    private func copyReportToClipboard() {
        let shareText = generateShareText()
        UIPasteboard.general.string = shareText

        // 触觉反馈
        let feedbackGenerator = UINotificationFeedbackGenerator()
        feedbackGenerator.notificationOccurred(.success)

        // 显示Toast提示
        showToast("ai_report.detail.report_copied".localized)
        print("📋 报告已复制到剪贴板")
    }

    /**
     * 显示Toast提示
     */
    private func showToast(_ message: String) {
        toastMessage = message
        withAnimation {
            showingToast = true
        }

        // 2秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation {
                showingToast = false
            }
        }
    }

    /// 生成分享文本
    private func generateShareText() -> String {
        let header = """
        【\(report.reportType.displayName)】
        分析对象：\(report.memberName)（\(report.memberRole)，\(report.memberAge)岁）
        生成时间：\(DateFormatter.fullDateTime.string(from: report.createdAt))

        """

        let content = report.content

        let footer = """


        ——————————————————
        由转团团AI分析功能生成
        """

        return header + content + footer
    }
}

// MARK: - CoreData版本的AIReportDetailView

/**
 * 基于CoreData AIReport实体的报告详情页面
 */
struct AIReportDetailCoreDataView: View {
    
    let aiReport: AIReport
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 报告头部信息
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Label(aiReport.reportTypeDisplayName, systemImage: reportTypeIcon)
                                .font(.headline)
                                .foregroundColor(.blue)
                            
                            Spacer()
                            
                            Text(aiReport.formattedCreatedAt)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        if let member = aiReport.member {
                            HStack(spacing: 12) {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("分析对象")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Text("\(member.displayName) · \(member.roleDisplayName) · \(member.age)岁")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                }
                                
                                Spacer()
                            }
                        }
                        
                        if let summary = aiReport.inputDataSummary {
                            Divider()
                            
                            Text(summary)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 报告内容
                    VStack(alignment: .leading, spacing: 16) {
                        Text("分析内容")
                            .font(.headline)
                        
                        MarkdownText(content: aiReport.content ?? "")
                            .font(.body)
                            .lineSpacing(4)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
                .padding()
            }
            .navigationTitle("AI分析报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingShareSheet = true
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    private var reportTypeIcon: String {
        switch aiReport.reportType ?? "analysis" {
        case "analysis":
            return "chart.line.uptrend.xyaxis"
        case "growth":
            return "heart.text.square"
        default:
            return "doc.text"
        }
    }
    
    private func generateShareText() -> String {
        let header = """
        【\(aiReport.reportTypeDisplayName)】
        分析对象：\(aiReport.member?.displayName ?? "未知")
        生成时间：\(aiReport.formattedCreatedAt)
        
        """
        
        let content = aiReport.content ?? ""
        
        let footer = """
        
        
        ——————————————————
        由转团团AI分析功能生成
        """
        
        return header + content + footer
    }
}

// MARK: - Supporting Views

/**
 * 增强的Markdown文本渲染器
 * 支持iOS 15.6+的markdown格式渲染
 */
struct MarkdownText: View {
    let content: String

    init(content: String) {
        self.content = content
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(parseMarkdownContent(), id: \.id) { element in
                renderMarkdownElement(element)
            }
        }
        .textSelection(.enabled)
    }

    // MARK: - Markdown解析

    /**
     * 清理markdown内容，移除开头的markdown标记
     */
    private func cleanMarkdownContent(_ content: String) -> String {
        var cleanedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除开头的```markdown标记
        if cleanedContent.hasPrefix("```markdown") {
            cleanedContent = String(cleanedContent.dropFirst(11))
        }

        // 移除开头的```标记
        if cleanedContent.hasPrefix("```") {
            cleanedContent = String(cleanedContent.dropFirst(3))
        }

        // 移除结尾的```标记
        if cleanedContent.hasSuffix("```") {
            cleanedContent = String(cleanedContent.dropLast(3))
        }

        return cleanedContent.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /**
     * 解析markdown内容为结构化元素
     */
    private func parseMarkdownContent() -> [MarkdownElement] {
        // 清理内容，移除开头的markdown标记
        let cleanedContent = cleanMarkdownContent(content)
        let lines = cleanedContent.components(separatedBy: .newlines)
        var elements: [MarkdownElement] = []
        var currentParagraph: [String] = []

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            if trimmedLine.isEmpty {
                // 空行，结束当前段落
                if !currentParagraph.isEmpty {
                    elements.append(MarkdownElement(
                        id: UUID(),
                        type: .paragraph,
                        content: currentParagraph.joined(separator: " "),
                        number: nil
                    ))
                    currentParagraph.removeAll()
                }
            } else if trimmedLine.hasPrefix("# ") {
                // 一级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading1,
                    content: String(trimmedLine.dropFirst(2)),
                    number: nil
                ))
            } else if trimmedLine.hasPrefix("## ") {
                // 二级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading2,
                    content: String(trimmedLine.dropFirst(3)),
                    number: nil
                ))
            } else if trimmedLine.hasPrefix("### ") {
                // 三级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading3,
                    content: String(trimmedLine.dropFirst(4)),
                    number: nil
                ))
            } else if trimmedLine.hasPrefix("- ") || trimmedLine.hasPrefix("* ") {
                // 无序列表项
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .listItem,
                    content: String(trimmedLine.dropFirst(2)),
                    number: nil
                ))
            } else if isNumberedListItem(trimmedLine) {
                // 编号列表项
                finalizeParagraph(&currentParagraph, &elements)
                let (content, number) = extractNumberedListContent(from: trimmedLine)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .numberedListItem,
                    content: content,
                    number: number
                ))
            } else {
                // 普通文本，添加到当前段落
                currentParagraph.append(trimmedLine)
            }
        }

        // 处理最后的段落
        finalizeParagraph(&currentParagraph, &elements)

        return elements
    }

    /**
     * 检查是否为编号列表项
     */
    private func isNumberedListItem(_ line: String) -> Bool {
        // 匹配 "数字. " 格式，如 "1. "、"2. "、"10. " 等
        let pattern = #"^\d+\.\s+"#
        return line.range(of: pattern, options: .regularExpression) != nil
    }

    /**
     * 提取编号列表的内容和编号
     */
    private func extractNumberedListContent(from line: String) -> (content: String, number: Int) {
        // 找到第一个 ". " 的位置，然后提取编号和内容
        if let dotRange = line.range(of: ". ") {
            let numberString = String(line[..<dotRange.lowerBound])
            let number = Int(numberString) ?? 1
            let contentStartIndex = line.index(dotRange.upperBound, offsetBy: 0)
            let content = String(line[contentStartIndex...])
            return (content, number)
        }
        return (line, 1)
    }

    /**
     * 完成当前段落的处理
     */
    private func finalizeParagraph(_ currentParagraph: inout [String], _ elements: inout [MarkdownElement]) {
        if !currentParagraph.isEmpty {
            elements.append(MarkdownElement(
                id: UUID(),
                type: .paragraph,
                content: currentParagraph.joined(separator: " "),
                number: nil
            ))
            currentParagraph.removeAll()
        }
    }

    // MARK: - 渲染方法

    /**
     * 渲染markdown元素
     * 参考ztt1的样式设计，优化字体、间距和颜色
     */
    @ViewBuilder
    private func renderMarkdownElement(_ element: MarkdownElement) -> some View {
        switch element.type {
        case .heading1:
            Text(element.content)
                .font(.system(size: 22, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .padding(.vertical, 12)
                .padding(.bottom, 4)
                .frame(maxWidth: .infinity, alignment: .leading)
        case .heading2:
            Text(element.content)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .padding(.vertical, 10)
                .padding(.bottom, 2)
                .frame(maxWidth: .infinity, alignment: .leading)
        case .heading3:
            Text(element.content)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .padding(.vertical, 8)
                .frame(maxWidth: .infinity, alignment: .leading)
        case .paragraph:
            Text(parseInlineMarkdown(element.content))
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineSpacing(8)
                .fixedSize(horizontal: false, vertical: true)
                .padding(.vertical, 6)
                .frame(maxWidth: .infinity, alignment: .leading)
        case .listItem:
            HStack(alignment: .top, spacing: 16) {
                // 自定义圆点
                ZStack {
                    Circle()
                        .fill(Color(hex: "#74c07f"))
                        .frame(width: 6, height: 6)
                }
                .padding(.top, 8)

                Text(parseInlineMarkdown(element.content))
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineSpacing(8)
                    .fixedSize(horizontal: false, vertical: true)

                Spacer()
            }
            .padding(.leading, 20)
            .padding(.vertical, 4)
        case .numberedListItem:
            HStack(alignment: .top, spacing: 16) {
                // 数字标签
                ZStack {
                    Circle()
                        .fill(Color(hex: "#74c07f").opacity(0.15))
                        .frame(width: 24, height: 24)

                    Text("\(element.number ?? 1)")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(Color(hex: "#74c07f"))
                }
                .padding(.top, 2)

                Text(parseInlineMarkdown(element.content))
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineSpacing(8)
                    .fixedSize(horizontal: false, vertical: true)

                Spacer()
            }
            .padding(.leading, 20)
            .padding(.vertical, 4)
        }
    }

    /**
     * 解析行内markdown格式（粗体、斜体等）
     * 参考ztt1的处理方式，优化粗体文本渲染
     */
    private func parseInlineMarkdown(_ text: String) -> AttributedString {
        var attributedString = AttributedString(text)

        // 处理粗体 **text**
        let boldPattern = #"\*\*(.*?)\*\*"#
        if let regex = try? NSRegularExpression(pattern: boldPattern) {
            let matches = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            for match in matches.reversed() {
                if let range = Range(match.range, in: text) {
                    let boldText = String(text[range])
                    let content = String(boldText.dropFirst(2).dropLast(2))
                    if let attributedRange = Range(match.range, in: attributedString) {
                        attributedString.replaceSubrange(attributedRange, with: AttributedString(content))
                        if let newRange = attributedString.range(of: content) {
                            // 使用系统字体的粗体版本，保持与ztt1一致的样式
                            attributedString[newRange].font = .system(size: 16, weight: .semibold)
                        }
                    }
                }
            }
        }

        return attributedString
    }
}

// MARK: - Markdown数据模型

/**
 * Markdown元素类型
 */
enum MarkdownElementType {
    case heading1
    case heading2
    case heading3
    case paragraph
    case listItem
    case numberedListItem
}

/**
 * Markdown元素
 */
struct MarkdownElement {
    let id: UUID
    let type: MarkdownElementType
    let content: String
    let number: Int? // 用于编号列表项
}

/**
 * Toast提示视图
 */
struct ToastView: View {
    let message: String

    var body: some View {
        Text(message)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.black.opacity(0.8))
            )
            .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
    }
}

/**
 * 分享功能
 */
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Button Styles

/**
 * 按钮按压效果样式
 */
struct PressedButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}


