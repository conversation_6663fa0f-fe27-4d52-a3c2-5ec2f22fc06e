//
//  DataDiagnosticView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import SwiftUI

/**
 * 数据诊断调试视图
 * 用于开发和测试阶段的数据问题诊断和修复
 */
struct DataDiagnosticView: View {
    
    @StateObject private var diagnosticTool = DataDiagnosticTool.shared
    @StateObject private var multiLayerStorage = MultiLayerStorageManager.shared
    @State private var showingReportDetail = false
    @State private var reportText = ""
    
    var body: some View {
        NavigationView {
            List {
                // 状态概览
                Section("诊断状态") {
                    HStack {
                        Image(systemName: diagnosticTool.isRunning ? "gear.circle.fill" : "checkmark.circle.fill")
                            .foregroundColor(diagnosticTool.isRunning ? .orange : .green)
                        
                        VStack(alignment: .leading) {
                            Text(diagnosticTool.isRunning ? "诊断运行中..." : "诊断就绪")
                                .font(.headline)
                            
                            if let lastDate = diagnosticTool.lastDiagnosticDate {
                                Text("上次诊断: \(lastDate, style: .relative)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                    }
                }
                
                // 快速操作
                Section("快速操作") {
                    Button(action: {
                        Task {
                            let _ = await diagnosticTool.performFullDiagnostic()
                        }
                    }) {
                        Label("执行完整诊断", systemImage: "magnifyingglass.circle")
                    }
                    .disabled(diagnosticTool.isRunning)
                    
                    Button(action: {
                        Task {
                            let _ = await diagnosticTool.performDataRepair()
                        }
                    }) {
                        Label("执行数据修复", systemImage: "wrench.and.screwdriver")
                    }
                    .disabled(diagnosticTool.isRunning)
                    
                    Button(action: {
                        Task {
                            await multiLayerStorage.performDataRecovery()
                        }
                    }) {
                        Label("多层存储恢复", systemImage: "arrow.clockwise.circle")
                    }
                    .disabled(diagnosticTool.isRunning)
                }
                
                // 诊断报告
                if let report = diagnosticTool.lastReport {
                    Section("最新诊断报告") {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("诊断时间")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Spacer()
                                Text(report.timestamp, style: .date)
                                    .font(.caption)
                            }
                            
                            Divider()
                            
                            // 问题概览
                            HStack {
                                VStack(alignment: .leading) {
                                    Text("\(report.totalIssues)")
                                        .font(.title2)
                                        .fontWeight(.bold)
                                    Text("总问题")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                VStack(alignment: .leading) {
                                    Text("\(report.criticalIssues)")
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(.red)
                                    Text("严重问题")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                VStack(alignment: .leading) {
                                    Text("\(report.fixedIssues)")
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(.green)
                                    Text("已修复")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            
                            Divider()
                            
                            // 数据统计
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("用户数据:")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text("\(report.userAnalysis.totalUsers) 用户")
                                        .font(.caption)
                                }
                                
                                HStack {
                                    Text("成员数据:")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text("\(report.memberAnalysis.totalMembers) 成员")
                                        .font(.caption)
                                }
                                
                                HStack {
                                    Text("积分记录:")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text("\(report.pointRecordAnalysis.totalRecords) 记录")
                                        .font(.caption)
                                }
                            }
                        }
                        .padding(.vertical, 4)
                        
                        Button("查看详细报告") {
                            reportText = diagnosticTool.generateReportText(report)
                            showingReportDetail = true
                        }
                    }
                }
                
                // 多层存储状态
                Section("多层存储状态") {
                    HStack {
                        VStack(alignment: .leading) {
                            Text("恢复状态")
                                .font(.headline)
                            
                            switch multiLayerStorage.recoveryStatus {
                            case .idle:
                                Text("空闲")
                                    .foregroundColor(.secondary)
                            case .recovering:
                                Text("恢复中...")
                                    .foregroundColor(.orange)
                            case .completed(let count):
                                Text("已恢复 \(count) 项")
                                    .foregroundColor(.green)
                            case .failed(let error):
                                Text("失败: \(error.localizedDescription)")
                                    .foregroundColor(.red)
                            }
                        }
                        
                        Spacer()
                        
                        if let lastRecovery = multiLayerStorage.lastRecoveryDate {
                            VStack(alignment: .trailing) {
                                Text("上次恢复")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text(lastRecovery, style: .relative)
                                    .font(.caption)
                            }
                        }
                    }
                }
                
                // 调试工具
                Section("调试工具") {
                    Button("检查数据一致性") {
                        let inconsistencies = multiLayerStorage.checkDataConsistency()
                        print("数据一致性检查结果: \(inconsistencies)")
                    }
                    
                    Button("修复数据不一致") {
                        Task {
                            await multiLayerStorage.repairDataInconsistencies()
                        }
                    }
                    
                    Button("清除诊断历史") {
                        UserDefaults.standard.removeObject(forKey: "last_diagnostic_date")
                        diagnosticTool.lastDiagnosticDate = nil
                        diagnosticTool.lastReport = nil
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("数据诊断")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                let _ = await diagnosticTool.performFullDiagnostic()
            }
        }
        .sheet(isPresented: $showingReportDetail) {
            NavigationView {
                ScrollView {
                    Text(reportText)
                        .font(.system(.body, design: .monospaced))
                        .padding()
                }
                .navigationTitle("诊断报告")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("完成") {
                            showingReportDetail = false
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Preview

struct DataDiagnosticView_Previews: PreviewProvider {
    static var previews: some View {
        DataDiagnosticView()
    }
}
