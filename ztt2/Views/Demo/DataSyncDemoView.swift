//
//  DataSyncDemoView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import SwiftUI

/**
 * 数据同步功能演示视图
 * 展示多层存储、数据诊断、自动恢复等功能的使用
 */
struct DataSyncDemoView: View {
    
    @EnvironmentObject private var multiLayerStorage: MultiLayerStorageManager
    @EnvironmentObject private var diagnosticTool: DataDiagnosticTool
    @EnvironmentObject private var dataRecoveryMonitor: DataRecoveryMonitor
    @EnvironmentObject private var cloudKitSyncMonitor: CloudKitSyncMonitor
    
    @State private var testKey = "demo_test_key"
    @State private var testValue = "演示数据值"
    @State private var retrievedValue: String = ""
    @State private var showingDiagnosticReport = false
    @State private var diagnosticReportText = ""
    
    var body: some View {
        NavigationView {
            List {
                // 多层存储演示
                Section("多层存储演示") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            TextField("测试键", text: $testKey)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                            
                            TextField("测试值", text: $testValue)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        HStack {
                            Button("存储数据") {
                                multiLayerStorage.store(testValue, forKey: testKey)
                            }
                            .buttonStyle(.borderedProminent)
                            
                            Button("恢复数据") {
                                if let value: String = multiLayerStorage.retrieve(String.self, forKey: testKey) {
                                    retrievedValue = value
                                } else {
                                    retrievedValue = "未找到数据"
                                }
                            }
                            .buttonStyle(.bordered)
                        }
                        
                        if !retrievedValue.isEmpty {
                            Text("恢复的数据: \(retrievedValue)")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                    .padding(.vertical, 4)
                }
                
                // 数据恢复状态
                Section("数据恢复状态") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: dataRecoveryMonitor.isMonitoring ? "eye.fill" : "eye.slash")
                                .foregroundColor(dataRecoveryMonitor.isMonitoring ? .green : .gray)
                            
                            VStack(alignment: .leading) {
                                Text(dataRecoveryMonitor.isMonitoring ? "监控中" : "未监控")
                                    .font(.headline)
                                
                                if let lastCheck = dataRecoveryMonitor.lastCheckDate {
                                    Text("上次检查: \(lastCheck, style: .relative)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing) {
                                Text("\(dataRecoveryMonitor.recoveryTriggeredCount)")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                Text("恢复次数")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        HStack {
                            Button("手动检查") {
                                Task {
                                    await dataRecoveryMonitor.triggerManualCheck()
                                }
                            }
                            .buttonStyle(.bordered)
                            
                            Button("重置状态") {
                                dataRecoveryMonitor.resetMonitoringState()
                            }
                            .buttonStyle(.bordered)
                        }
                    }
                }
                
                // CloudKit同步状态
                Section("CloudKit同步状态") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: cloudKitSyncMonitor.syncStatus.isActive ? "icloud.and.arrow.up" : "icloud")
                                .foregroundColor(cloudKitSyncMonitor.syncStatus.isActive ? .blue : .gray)
                            
                            VStack(alignment: .leading) {
                                Text(cloudKitSyncMonitor.syncStatus.displayText)
                                    .font(.headline)
                                
                                if let lastSync = cloudKitSyncMonitor.lastSyncDate {
                                    Text("上次同步: \(lastSync, style: .relative)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing) {
                                Text("\(cloudKitSyncMonitor.getSyncHealthScore())%")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(healthScoreColor(cloudKitSyncMonitor.getSyncHealthScore()))
                                Text("健康度")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        HStack {
                            Text("成功: \(cloudKitSyncMonitor.syncSuccessCount)")
                                .font(.caption)
                                .foregroundColor(.green)
                            
                            Text("失败: \(cloudKitSyncMonitor.syncFailureCount)")
                                .font(.caption)
                                .foregroundColor(.red)
                            
                            Spacer()
                            
                            Button("手动同步") {
                                Task {
                                    await cloudKitSyncMonitor.triggerManualSync()
                                }
                            }
                            .buttonStyle(.bordered)
                            .disabled(cloudKitSyncMonitor.syncStatus.isActive)
                        }
                    }
                }
                
                // 数据诊断
                Section("数据诊断") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Image(systemName: diagnosticTool.isRunning ? "stethoscope.circle.fill" : "stethoscope")
                                .foregroundColor(diagnosticTool.isRunning ? .orange : .blue)
                            
                            VStack(alignment: .leading) {
                                Text(diagnosticTool.isRunning ? "诊断中..." : "诊断就绪")
                                    .font(.headline)
                                
                                if let lastDiagnostic = diagnosticTool.lastDiagnosticDate {
                                    Text("上次诊断: \(lastDiagnostic, style: .relative)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            
                            Spacer()
                        }
                        
                        if let report = diagnosticTool.lastReport {
                            HStack {
                                VStack {
                                    Text("\(report.totalIssues)")
                                        .font(.title3)
                                        .fontWeight(.bold)
                                    Text("总问题")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                VStack {
                                    Text("\(report.criticalIssues)")
                                        .font(.title3)
                                        .fontWeight(.bold)
                                        .foregroundColor(.red)
                                    Text("严重问题")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                VStack {
                                    Text("\(report.userAnalysis.totalUsers)")
                                        .font(.title3)
                                        .fontWeight(.bold)
                                    Text("用户数")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                VStack {
                                    Text("\(report.memberAnalysis.totalMembers)")
                                        .font(.title3)
                                        .fontWeight(.bold)
                                    Text("成员数")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                        
                        HStack {
                            Button("执行诊断") {
                                Task {
                                    let _ = await diagnosticTool.performFullDiagnostic()
                                }
                            }
                            .buttonStyle(.borderedProminent)
                            .disabled(diagnosticTool.isRunning)
                            
                            Button("数据修复") {
                                Task {
                                    let _ = await diagnosticTool.performDataRepair()
                                }
                            }
                            .buttonStyle(.bordered)
                            .disabled(diagnosticTool.isRunning)
                            
                            if diagnosticTool.lastReport != nil {
                                Button("查看报告") {
                                    if let report = diagnosticTool.lastReport {
                                        diagnosticReportText = diagnosticTool.generateReportText(report)
                                        showingDiagnosticReport = true
                                    }
                                }
                                .buttonStyle(.bordered)
                            }
                        }
                    }
                }
                
                // 快速操作
                Section("快速操作") {
                    Button("执行完整数据恢复") {
                        Task {
                            await multiLayerStorage.performDataRecovery()
                        }
                    }
                    
                    Button("检查数据一致性") {
                        let inconsistencies = multiLayerStorage.checkDataConsistency()
                        print("数据一致性检查结果: \(inconsistencies)")
                    }
                    
                    Button("修复数据不一致") {
                        Task {
                            await multiLayerStorage.repairDataInconsistencies()
                        }
                    }
                }
            }
            .navigationTitle("数据同步演示")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingDiagnosticReport) {
            NavigationView {
                ScrollView {
                    Text(diagnosticReportText)
                        .font(.system(.body, design: .monospaced))
                        .padding()
                }
                .navigationTitle("诊断报告")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("完成") {
                            showingDiagnosticReport = false
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func healthScoreColor(_ score: Int) -> Color {
        switch score {
        case 90...100:
            return .green
        case 70..<90:
            return .orange
        default:
            return .red
        }
    }
}

// MARK: - Preview

struct DataSyncDemoView_Previews: PreviewProvider {
    static var previews: some View {
        DataSyncDemoView()
            .environmentObject(MultiLayerStorageManager.shared)
            .environmentObject(DataDiagnosticTool.shared)
            .environmentObject(DataRecoveryMonitor.shared)
            .environmentObject(CloudKitSyncMonitor.shared)
    }
}
