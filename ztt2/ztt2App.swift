//
//  ztt2App.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import RevenueCat

@main
struct ztt2App: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var subscriptionSyncManager = SubscriptionSyncManager.shared
    @StateObject private var trialManager = TrialManager.shared
    @StateObject private var multiLayerStorage = MultiLayerStorageManager.shared
    @StateObject private var diagnosticTool = DataDiagnosticTool.shared
    @StateObject private var dataRecoveryMonitor = DataRecoveryMonitor.shared
    @StateObject private var cloudKitSyncMonitor = CloudKitSyncMonitor.shared
    @State private var isInitializing = true
    @State private var dataRecoveryCompleted = false

    init() {
        print("🚀 应用启动 - 统一CloudKit多设备同步模式")

        // 初始化API密钥
        setupAPIKey()

        // 配置RevenueCat
        configureRevenueCat()
    }

    var body: some Scene {
        WindowGroup {
            Group {
                if isInitializing {
                    EnhancedLaunchScreenView()
                        .onAppear {
                            initializeApp()
                        }
                } else {
                    ContentView()
                        .environment(\.managedObjectContext, persistenceController.container.viewContext)
                        .environmentObject(dataManager)
                        .environmentObject(authManager)
                        .environmentObject(revenueCatManager)
                        .environmentObject(subscriptionService)
                        .environmentObject(subscriptionSyncManager)
                        .environmentObject(trialManager)
                        .environmentObject(multiLayerStorage)
                        .environmentObject(diagnosticTool)
                        .environmentObject(dataRecoveryMonitor)
                        .environmentObject(cloudKitSyncMonitor)
                }
            }
        }
    }

    // MARK: - Private Methods

    /**
     * 设置API密钥
     */
    private func setupAPIKey() {
        let keychainManager = KeychainManager.shared

        // 如果Keychain中没有API密钥，则设置默认密钥
        if !keychainManager.hasAPIKey() {
            keychainManager.saveAPIKey("sk-eb2dc94c4f594097b7747421169b9110")
            print("🔐 已设置默认API密钥")
        } else {
            print("🔐 API密钥已存在于Keychain中")
        }
    }

    /**
     * 配置RevenueCat
     */
    private func configureRevenueCat() {
        // ⚠️ 重要：请将此API Key替换为您从RevenueCat Dashboard获取的真实API Key
        // 1. 登录 https://app.revenuecat.com/
        // 2. 创建新项目或选择现有项目
        // 3. 在项目设置中找到 API Keys
        // 4. 复制 Apple App Store 的 Public API Key
        // 5. 将下面的 "appl_YOUR_REVENUECAT_API_KEY_HERE" 替换为真实的API Key
        let apiKey = "appl_AoQsgjVJvNmhqMzpYtObdNflzsn"

        // 获取当前用户ID (如果已登录)
        let userId = AuthenticationManager.shared.currentUser?.appleUserID

        // 配置RevenueCat
        Task { @MainActor in
            RevenueCatManager.shared.configure(apiKey: apiKey, userId: userId)

            // 配置订阅服务
            SubscriptionService.shared.configure(userId: userId)
        }

        print("🔧 RevenueCat配置完成")
    }

    /**
     * 初始化应用
     */
    private func initializeApp() {
        // 立即开始登录状态检查，避免在ContentView中再次触发
        authManager.checkLoginStatus()

        // 启动数据恢复流程
        Task {
            await performStartupDataRecovery()

            await MainActor.run {
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { // 增加时间让Logo旋转动画完成
                    // 刷新试用状态，确保状态正确
                    print("🔄 刷新试用状态...")
                    trialManager.refreshTrialStatus()

                    // 启动数据恢复监控
                    dataRecoveryMonitor.startMonitoring()

                    // 触发初始CloudKit同步
                    Task {
                        await cloudKitSyncMonitor.triggerManualSync()
                    }

                    print("✅ 应用初始化完成 - CloudKit同步已启用")
                    isInitializing = false
                }
            }
        }
    }

    /**
     * 执行启动时的数据恢复
     */
    private func performStartupDataRecovery() async {
        print("🔄 开始启动时数据恢复...")

        // 1. 检查是否需要数据恢复
        let needsRecovery = await checkIfDataRecoveryNeeded()

        if needsRecovery {
            print("📦 检测到需要数据恢复，开始恢复流程...")

            // 2. 执行多层存储数据恢复
            await multiLayerStorage.performDataRecovery()

            // 3. 执行数据一致性检查和修复
            await diagnosticTool.performDataRepair()

            // 4. 触发试用管理器的数据恢复
            await trialManager.triggerDataRecovery()

            print("✅ 启动时数据恢复完成")
        } else {
            print("ℹ️ 数据状态正常，跳过恢复流程")
        }

        await MainActor.run {
            dataRecoveryCompleted = true
        }
    }

    /**
     * 检查是否需要数据恢复
     */
    private func checkIfDataRecoveryNeeded() async -> Bool {
        // 检查关键数据是否存在
        let hasUser = dataManager.currentUser != nil
        let hasTrialData = trialManager.hasReceivedTrial || trialManager.trialExpirationDate != nil

        // 检查上次恢复时间
        let lastRecoveryDate = multiLayerStorage.lastRecoveryDate
        let daysSinceLastRecovery = lastRecoveryDate?.timeIntervalSinceNow ?? -TimeInterval.infinity
        let needsPeriodicRecovery = abs(daysSinceLastRecovery) > 24 * 60 * 60 // 超过24小时

        // 检查数据一致性
        let inconsistencies = multiLayerStorage.checkDataConsistency()
        let hasInconsistencies = !inconsistencies.isEmpty

        // 如果缺少关键数据、需要定期恢复或存在不一致，则需要恢复
        let needsRecovery = !hasUser || !hasTrialData || needsPeriodicRecovery || hasInconsistencies

        print("🔍 数据恢复需求检查:")
        print("  - 有用户数据: \(hasUser)")
        print("  - 有试用数据: \(hasTrialData)")
        print("  - 需要定期恢复: \(needsPeriodicRecovery)")
        print("  - 有数据不一致: \(hasInconsistencies)")
        print("  - 需要恢复: \(needsRecovery)")

        return needsRecovery
    }
}
