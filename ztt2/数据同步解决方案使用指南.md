# 数据同步解决方案使用指南

## 📖 概述

本指南介绍如何使用ztt2项目中新实施的数据同步解决方案，包括多层存储、数据诊断、自动恢复等功能。

## 🏗️ 架构概览

### 核心组件

1. **MultiLayerStorageManager** - 多层存储管理器
2. **DataDiagnosticTool** - 数据诊断工具
3. **DataRecoveryMonitor** - 数据恢复监控器
4. **CloudKitSyncMonitor** - CloudKit同步监控器
5. **CoreDataManager** - 增强的核心数据管理器

### 存储层级

```
优先级：CoreData > NSUbiquitousKeyValueStore > UserDefaults
```

## 🚀 快速开始

### 1. 基本使用

```swift
// 获取多层存储管理器实例
let storage = MultiLayerStorageManager.shared

// 存储数据到所有层
storage.store("用户偏好值", forKey: "user_preference")

// 从多层存储中恢复数据
let value: String? = storage.retrieve(String.self, forKey: "user_preference")
```

### 2. 数据诊断

```swift
// 获取诊断工具实例
let diagnostic = DataDiagnosticTool.shared

// 执行完整诊断
Task {
    let report = await diagnostic.performFullDiagnostic()
    print("发现 \(report.totalIssues) 个问题")
}

// 执行数据修复
Task {
    let report = await diagnostic.performDataRepair()
    print("修复了 \(report.fixedIssues) 个问题")
}
```

### 3. 手动数据恢复

```swift
// 触发数据恢复
Task {
    await MultiLayerStorageManager.shared.performDataRecovery()
}

// 触发试用数据恢复
Task {
    await TrialManager.shared.triggerDataRecovery()
}
```

## 🔧 高级功能

### 1. 数据一致性检查

```swift
// 检查数据一致性
let inconsistencies = MultiLayerStorageManager.shared.checkDataConsistency()

if !inconsistencies.isEmpty {
    print("发现数据不一致:")
    for inconsistency in inconsistencies {
        print("- \(inconsistency)")
    }
    
    // 修复不一致
    Task {
        await MultiLayerStorageManager.shared.repairDataInconsistencies()
    }
}
```

### 2. CloudKit同步监控

```swift
// 获取同步监控器
let syncMonitor = CloudKitSyncMonitor.shared

// 手动触发同步
Task {
    await syncMonitor.triggerManualSync()
}

// 获取同步统计
let stats = syncMonitor.getSyncStatistics()
print("同步成功率: \(Int(stats.successRate * 100))%")
print("健康度评分: \(stats.healthScore)")
```

### 3. 数据恢复监控

```swift
// 获取恢复监控器
let recoveryMonitor = DataRecoveryMonitor.shared

// 启动监控
recoveryMonitor.startMonitoring()

// 手动触发检查
Task {
    await recoveryMonitor.triggerManualCheck()
}

// 重置监控状态
recoveryMonitor.resetMonitoringState()
```

## 🎛️ 调试工具

### 1. 数据诊断界面

在调试模式下，可以使用 `DataDiagnosticView` 进行可视化诊断：

```swift
import SwiftUI

struct DebugView: View {
    var body: some View {
        NavigationView {
            DataDiagnosticView()
        }
    }
}
```

### 2. 诊断报告导出

```swift
// 生成诊断报告
Task {
    let report = await DataDiagnosticTool.shared.performFullDiagnostic()
    let reportText = DataDiagnosticTool.shared.generateReportText(report)
    
    // 保存或分享报告
    print(reportText)
}
```

## 📊 监控和统计

### 1. 恢复状态监控

```swift
// 监听恢复状态变化
MultiLayerStorageManager.shared.$recoveryStatus
    .sink { status in
        switch status {
        case .completed(let count):
            print("恢复完成，恢复了 \(count) 项数据")
        case .failed(let error):
            print("恢复失败: \(error)")
        default:
            break
        }
    }
    .store(in: &cancellables)
```

### 2. 同步状态监控

```swift
// 监听同步状态变化
CloudKitSyncMonitor.shared.$syncStatus
    .sink { status in
        print("同步状态: \(status.displayText)")
    }
    .store(in: &cancellables)
```

## 🔔 通知系统

### 1. 监听数据恢复通知

```swift
NotificationCenter.default.addObserver(
    forName: .dataRecoveryCompleted,
    object: nil,
    queue: .main
) { notification in
    if let userInfo = notification.userInfo {
        let recoveryCount = userInfo["recoveryCount"] as? Int ?? 0
        print("数据恢复完成，恢复了 \(recoveryCount) 项")
    }
}
```

### 2. 监听同步通知

```swift
NotificationCenter.default.addObserver(
    forName: NSNotification.Name("CloudKitSyncSuccess"),
    object: nil,
    queue: .main
) { _ in
    print("CloudKit同步成功")
}

NotificationCenter.default.addObserver(
    forName: NSNotification.Name("CloudKitSyncFailed"),
    object: nil,
    queue: .main
) { notification in
    if let error = notification.userInfo?["error"] as? Error {
        print("CloudKit同步失败: \(error)")
    }
}
```

## ⚙️ 配置选项

### 1. 监控配置

```swift
// 数据恢复监控配置
let monitor = DataRecoveryMonitor.shared

// 监控间隔：5分钟
// 严重问题检查间隔：1分钟
// 最大自动恢复尝试次数：3次
```

### 2. 同步重试配置

```swift
// CloudKit同步重试配置
let syncMonitor = CloudKitSyncMonitor.shared

// 最大重试次数：5次
// 基础重试延迟：2秒
// 最大重试延迟：60秒
```

## 🧪 测试

### 1. 运行测试

```bash
# 运行数据同步解决方案测试
xcodebuild test -scheme ztt2 -destination 'platform=iOS Simulator,name=iPhone 15' -only-testing:ztt2Tests/DataSyncSolutionTests
```

### 2. 性能测试

```swift
// 多层存储性能测试
func testMultiLayerStoragePerformance() {
    measure {
        // 测试存储和检索性能
    }
}
```

## 🚨 故障排除

### 1. 常见问题

**问题：数据恢复失败**
```swift
// 检查恢复状态
switch MultiLayerStorageManager.shared.recoveryStatus {
case .failed(let error):
    print("恢复失败原因: \(error)")
    // 手动重试
    Task {
        await MultiLayerStorageManager.shared.performDataRecovery()
    }
default:
    break
}
```

**问题：CloudKit同步失败**
```swift
// 检查同步状态
let stats = CloudKitSyncMonitor.shared.getSyncStatistics()
if stats.healthScore < 80 {
    print("同步健康度较低: \(stats.healthScore)%")
    // 手动触发同步
    Task {
        await CloudKitSyncMonitor.shared.triggerManualSync()
    }
}
```

### 2. 调试技巧

1. **启用详细日志**：所有组件都包含详细的控制台日志
2. **使用诊断界面**：通过 `DataDiagnosticView` 可视化查看数据状态
3. **监控通知**：监听相关通知来跟踪系统状态
4. **检查一致性**：定期运行数据一致性检查

## 📝 最佳实践

1. **定期诊断**：建议每周运行一次完整诊断
2. **监控健康度**：保持同步健康度在80%以上
3. **及时修复**：发现严重问题时立即修复
4. **备份重要数据**：关键操作前先备份数据
5. **测试恢复**：定期测试数据恢复功能

## 🔗 相关文档

- [数据同步问题解决方案实施总结.md](./数据同步问题解决方案实施总结.md)
- [DataSyncSolutionTests.swift](./Tests/DataSyncSolutionTests.swift)

---

如有问题或建议，请联系开发团队。
